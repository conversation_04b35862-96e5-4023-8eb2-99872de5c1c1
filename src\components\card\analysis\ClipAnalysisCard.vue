<template>
  <BaseCard :width="'400px'" :title="'剖切分析'" :closed-method="close">
    <!-- <el-row class="buffer-input">
      <el-col class="input-label" :span="8">
        <div>经度</div>
      </el-col>
      <el-col :span="16">
        <el-input-number @change="change" :width="'242px'" :height="'40px'" v-model="currPosition.longitude">
        </el-input-number>
      </el-col>
    </el-row>
    <el-row class="buffer-input">
      <el-col class="input-label" :span="8">
        <div>纬度</div>
      </el-col>
      <el-col :span="16">
        <el-input-number @change="change" :width="'242px'" :height="'40px'" v-model="currPosition.latitude">
        </el-input-number>
      </el-col>
    </el-row> -->
    <el-row class="buffer-input">
      <el-col class="input-label" :span="8"> 高度: </el-col>
      <el-col :span="16">
        <el-input-number
          :step="0.1"
          :disabled="currPosition.heightDisabled"
          @change="change"
          :width="'242px'"
          :height="'40px'"
          class="custom-input-number"
          v-model="currPosition.height"
        >
        </el-input-number>
      </el-col>
    </el-row>
    <el-row class="buffer-input">
      <el-col class="input-label" :span="8"> 航向角: </el-col>
      <el-col :span="16">
        <el-input-number
          :disabled="currPosition.headingDisabled"
          @change="change"
          :width="'242px'"
          :height="'40px'"
          class="custom-input-number"
          v-model="currPosition.heading"
        >
        </el-input-number>
      </el-col>
    </el-row>
    <el-row class="buffer-input">
      <el-col class="input-label" :span="8"> 偏转角: </el-col>
      <el-col :span="16">
        <el-input-number
          :disabled="currPosition.pitchDisabled"
          @change="change"
          :width="'242px'"
          :height="'40px'"
          class="custom-input-number"
          v-model="currPosition.pitch"
        >
        </el-input-number>
      </el-col>
    </el-row>
    <!-- <el-row class="buffer-input">
      <el-col class="input-label" :span="8">
        <div>翻转角</div>
      </el-col>
      <el-col :span="16">
        <el-input-number @change="change" :width="'242px'" :height="'40px'" v-model="currPosition.roll">
        </el-input-number>
      </el-col>
    </el-row> -->
    <el-row class="clipmode-btns" justify="center">
      <el-button class="custom-confirm-btn" @click="changeMode(1)">横切</el-button>
      <el-button @click="changeMode(2)" class="custom-confirm-btn">纵切</el-button>
      <el-button @click="changeMode(3)" class="custom-confirm-btn">顶部</el-button>
    </el-row>
    <el-row class="clip-btns" justify="center">
      <el-button class="custom-confirm-btn" @click="clipping">绘制</el-button>
      <el-button @click="resetClip" class="custom-cancel-btn">清除</el-button>
    </el-row>
  </BaseCard>
</template>

<script setup lang="ts">
import { App } from '@/base/App'
import { onUnmounted, ref } from 'vue'
import BaseCard from '@/components/card/BaseCard.vue'
const close = () => {}
onUnmounted(() => {
  resetClip()
})

const resetClip = () => {
  tilesetOverlay && tilesetOverlay.delegate.clippingPlanes.removeAll();
}
const changeMode = (mode: number) => {
  currPosition.headingDisabled = false
  currPosition.pitchDisabled = false
  currPosition.heightDisabled = false
  if (mode === 3) {
    modelSection.height = currPosition.height = -0.1
    modelSection.heading = currPosition.heading = -90
    currPosition.headingDisabled = true
    currPosition.pitchDisabled = true
  }
  if (mode === 1) {
    modelSection.heading = currPosition.heading = 0
    modelSection.pitch = currPosition.pitch = -240
    currPosition.headingDisabled = true
    currPosition.heightDisabled = true
  }
  if (mode === 2) {
    modelSection.heading = currPosition.heading = 0
    modelSection.pitch = currPosition.pitch = 33
    currPosition.headingDisabled = true
    currPosition.heightDisabled = true
  }
}
const change = () => {
  if (modelSection) {
    console.log(currPosition)
    // App.getInstance().getViewer().entities.removeAll();
    // modelSection.longitude = currPosition.longitude
    // modelSection.latitude = currPosition.latitude
    modelSection.height = Number(currPosition.height)
    modelSection.heading = Number(currPosition.heading)
    modelSection.pitch = Number(currPosition.pitch)
    // modelSection.roll = currPosition.roll
    // App.getInstance().getViewer().entities.add(modelSection.plane)
  }
}

let tilesetOverlay: any;
let modelSection: any
const clipping = () => {
  resetClip()
  App.getInstance()
    .getPlotUtil()
    .draw(
      'point',
      (overlay: any) => {
        if (overlay) {
          console.log(overlay)
          const layers = App.getInstance().LayerManager.find(
            (layer: any) => layer._title === '地下管廊'
          )
          if (layers.length > 0) {
            const layer: any = layers[0]
            console.log(layer.getOverlays())
            const overlays = layer.getOverlays()
            if (overlays.length > 0) {
              tilesetOverlay = overlays[0];
              overlays[0].addClippingPlane(overlay.position)
              // overlays[0].modelSection.
              modelSection = overlays[0].modelSection
              // App.getInstance().getViewer().entities.add(modelSection.plane);
              // console.log(overlays[0].modelSection)
              // currPosition.longitude = modelSection.longitude;
              // currPosition.latitude = modelSection.latitude;
              currPosition.height = modelSection.height.toFixed(3)
              currPosition.heading = modelSection.heading
              currPosition.pitch = modelSection.pitch
              modelSection.pitch = currPosition.pitch = -240
              // currPosition.roll = modelSection.roll;
              // console.log(currPosition)
            }
          }
        }
      },
      {},
      true
    )
}
const currPosition = reactive({
  // longitude: 0,
  // latitude: 0,
  heightDisabled: false,
  headingDisabled: false,
  pitchDisabled: false,
  height: 0,
  heading: 0,
  pitch: 0
  // roll: 0,
})
</script>

<style lang="scss" scoped>
.clip-btns {
  padding: 30px 0px;
  .el-button {
    width: 115px;
    margin: 0px 30px;
    color: #fff;
  }
}
.clipmode-btns {
  padding: 5px 20px;
  .el-button {
    width: 70px;
    margin: 0px 20px;
    color: #fff;
  }
}

.input-label {
  font-size: 14px;
  text-align: left;
  padding-left: 45px;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN-Light;
  color: #ffffff;
}
.buffer-input {
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 10px;
  line-height: 40px;
}
// :deep(.el-input__wrapper) {
//   box-shadow: 0 0 0 1px #31bab2 inset
// }
// :deep(.el-input-number__increase) {
//   background: #31bab2;
//   border-left: none;
//   color: white;
// }
// :deep(.el-input-number__decrease) {
//   background: #31bab2;
//   border-right: none;
//   color: white
// }
</style>
