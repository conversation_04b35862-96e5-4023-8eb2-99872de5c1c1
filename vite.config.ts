import { fileURLToPath, URL } from 'node:url'
import { defineConfig, type PluginOption } from 'vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import vue from '@vitejs/plugin-vue'
import { viteStaticCopy } from 'vite-plugin-static-copy'
import viteCompression from 'vite-plugin-compression'
// https://vitejs.dev/config/
export default defineConfig((context) => {
  const mode = context.mode
  const base = './'
  const plugins: PluginOption[] = [
    vue(),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      dts: 'src/auto-import.d.ts',
      dirs: ['./src/hooks/*', './src/stores'],
      resolvers: [ElementPlusResolver()],
      eslintrc: {
        enabled: true, // <-- this
        filepath: './.eslintrc-auto-import.json'
      }
    }),
    Components({
      dts: 'src/components.d.ts',
      types: [
        {
          from: 'vue-router',
          names: ['RouterLink', 'RouterView']
        }
      ],
      // 默认导出 src/components 下的组件
      dirs: ['src/components'],
      extensions: ['vue', 'tsx'],
      resolvers: [
        // 使用element图标：icon-ep-...   使用iconify图标：icon-...
        IconsResolver({
          prefix: 'icon',
          enabledCollections: ['ep', 'solar']
        }),
        ElementPlusResolver()
      ]
    }),
    Icons({
      compiler: 'vue3',
      autoInstall: true
    })
  ]
  const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg|ttf|eot|woff)(\?.*)?$/i
  const cesiumLibraryRoot = './node_modules/bcgis-sdk/dist/resources/*'
  const cesiumLibraryCopyToRootPath = 'libs/bcgis-sdk/resources/' // 相对于打包后的路径
  plugins.push(
    viteStaticCopy({
      targets: [
        {
          src: cesiumLibraryRoot,
          dest: cesiumLibraryCopyToRootPath
        }
      ]
    })
  )

  plugins.push(
    viteCompression({
      verbose: true, // 默认即可
      disable: false, // 开启压缩(不禁用)，默认即可
      deleteOriginFile: false, // 删除源文件
      threshold: 5120, // 压缩前最小文件大小
      algorithm: 'gzip', // 压缩算法
      ext: '.gz' // 文件类型
    })
  )

  const server = {
    // https: false,
    host: true,
    open: false,
    cors: true,
    port: 5204,
    proxy: {
      '/api': {
        target: 'http://*************:61/api',
        // target: 'http://t64cwd.natappfree.cc/api',
        changeOrigin: true,
        rewrite: (path: string) => path.replace(/^\/api/, '')
      },
      '/object-server/open': {
        target: 'http://*************:61/',
        // target: 'http://t64cwd.natappfree.cc/',
        changeOrigin: true,
        rewrite: (path: string) => path.replace(/^\/object-server/, '')
      },
      '/object-server/admin': {
        target: 'http://*************:61/',
        // target: 'http://t64cwd.natappfree.cc/',
        changeOrigin: true,
        rewrite: (path: string) => path.replace(/^\/object-server/, '')
      },
      '/device-server/admin': {
        target: 'http://*************:61/',
        // target: 'http://t64cwd.natappfree.cc/',
        changeOrigin: true,
        rewrite: (path: string) => path.replace(/^\/device-server/, '')
      }
    }
  }
  const resolve = {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
  const css = {
    preprocessorOptions: {
      scss: {
        additionalData: '@use "@/assets/css/var.scss" as *;'
      }
    }
  }

  return {
    base,
    mode,
    plugins,
    server,
    resolve,
    css
  }
})
