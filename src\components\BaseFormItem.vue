<template>
  <el-form-item label="联系人" :prop="prop">
    <template #label>
      <span :class="[required ? 'required-form-label' : 'slot-form-label']">{{ label }}</span>
    </template>
    <slot></slot>
  </el-form-item>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    label: string
    prop?: string
    required?: boolean // 如果此项表单必填，则必传，值为 true
  }>(),
  {
    required: false
  }
)
</script>

<style scoped></style>
