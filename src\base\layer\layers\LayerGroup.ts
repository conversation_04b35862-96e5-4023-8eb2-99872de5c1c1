import { type LayerItem } from "../type/LayerItem";
import { BaseLayer } from "./BaseLayer";
import { LayerFactory } from "./LayerFactory";

/*
 * @Description: 图层组
 * @Autor: silei
 * @Date: 2023-08-02 15:58:39
 * @LastEditors: silei
 * @LastEditTime: 2023-09-05 09:56:57
 */
class SortedMap<K, V> {
  _map = new Map<K, V>();
  list: K[] = [];
  add(key: K, value: V) {
    this._map.set(key, value);
    this.list.push(key);
  }
  values() {
    const values = this.list.map((i) => this._map.get(i));
    return values;
  }
  keys() {
    return this.list;
  }
  get(key: K) {
    return this._map.get(key);
  }
  delete(key: K) {
    const index = this.list.findIndex((i) => i === key);
    if (index > -1) {
      this.list.splice(index, 1);
      this._map.delete(key);
    }
  }
  insert(index: number, key: K, value: V) {
    this.list = [...this.list.slice(0, index), key, ...this.list.slice(index)];
    this._map.set(key, value);
  }
  swap(index1: number, index2: number) {
    [this.list[index1], this.list[index2]] = [
      this.list[index2],
      this.list[index1],
    ];
  }
  findIndex(predicate: (value: K, index: number, obj: K[]) => boolean) {
    return this.list.findIndex(predicate);
  }
}
export class LayerGroup extends BaseLayer {
  protected declare _delegate: SortedMap<string, BaseLayer>;

  constructor(options: any) {
    super(options);
  }
  addToInternal(viewer: BC.Viewer) {
    const delegate = new SortedMap<string, BaseLayer>();
    this._delegate = delegate;
    for (const child of this.options.children) {
      let layer: BaseLayer;
      if (child.children && child.children instanceof Array) {
        layer = LayerFactory.crateLayer("group", child);
      } else {
        layer = LayerFactory.crateLayer(child.type, child);
      }
      if (layer) {
        delegate.add(layer.id, layer);
        layer.addTo(viewer, this);
      }
    }
    return delegate;
  }
  flyTo(): void {
    return;
  }
  set show(show: any) {
    this._show = show;
    for (const layer of this._delegate.values()) {
      (layer as BaseLayer).show = this._show;
    }
  }
  get show(): any {
    return this._show;
  }
  findLayer(layerId: string): BaseLayer | null {
    if (this._id === layerId) {
      return this;
    }
    for (const layer of this._delegate.values()) {
      const value = (layer as BaseLayer).findLayer(layerId);
      if (value) {
        return value;
      }
    }
    return null;
  }

  getLayerTree(): LayerItem {
    const children: LayerItem[] = [];
    for (const layer of this._delegate.values()) {
      children.push((layer as BaseLayer).getLayerTree());
    }
    return {
      id: this._id,
      title: this._title,
      show: !!children.find((l) => l.show),
      type: "group",
      children,
    };
  }
  /**
   * 移除子图层
   * @param layerId
   */
  removeChild(layerId: string) {
    const layer = this._delegate.get(layerId);
    if (layer) {
      layer.removeInternal();
      this._delegate.delete(layerId);
    }
  }
  removeInternal() {
    const keys = [...this._delegate.keys()];
    for (const layer of keys) {
      this.removeChild(layer);
    }
  }
  /**
   * 添加图层
   * @param layer
   */
  addLayer(layer: BaseLayer, index?: number) {
    layer.parent = this;
    if (index === 0 || index) {
      this._delegate.insert(index, layer.id, layer);
    } else {
      this._delegate.add(layer.id, layer);
    }
  }
  /**
   * 移除图层
   * @param layerId
   */
  removeLayer(layerId: string) {
    this._delegate.delete(layerId);
  }

  insertLayer(index: number, layer: BaseLayer) {
    layer.parent = this;
    this._delegate.insert(index, layer.id, layer);
  }

  findLayers(predicate: (layer: any) => boolean): BaseLayer[] {
    const layers: any = [];
    if (this._delegate) {
      for (const layer of this._delegate.values()) {
        if (predicate(layer)) {
          layers.push(layer);
        }
        if (layer instanceof LayerGroup) {
          layers.push(...layer.findLayers(predicate));
        }
      }
    }
    return layers;
  }
}
