// 出口文件
import Http from './request/index'
import { getAccessTokenFromCookie } from '../auth'

const hRequest = new Http({
  baseURL: import.meta.env.VITE_XHR_URL2,
  timeout: 10000,
  interceptors: {
    requestInterceptor: (config) => {
      // NProgress.start();
      // 携带token的拦截
      // const token = getToken();
      const token =
        getAccessTokenFromCookie() ?? window.localStorage.getItem('Access-Token') ?? null
      if (token !== null) {
        config.headers!['Access-Token'] = token
      }
      return config
    },
    requestInterceptorCatch: (err) => {
      return err
    },
    responseInterceptor: (res) => {
      // NProgress.done();
      return res
    },
    responseInterceptorCatch: (err) => {
      // err.toString().includes("timeout") !== null
      //   ? ElMessage({ type: "error", message: "超时，请重试" })
      //   : "";
      // NProgress.done();
      return err
    }
  }
})

export default hRequest
