@font-face {
  font-family: YouSheBiaoTi;
  font-display: fallback;
  src: local('YouSheBiaoTiHei'), url('../font/YouSheBiaoTiHei-2.ttf');
}
@font-face {
  font-family: DIN-Bold;
  font-display: fallback;
  src: local('DIN-Alternate-Bold'), url('../font/DIN-Alternate-Bold.ttf');
}
@font-face {
  font-family: Source-CN-Medium;
  font-display: fallback;
  src: local('SourceHanSansCN-Medium'), url('../font/SourceHanSansCN-Medium.otf');
}
@font-face {
  font-family: Source-CN-Normal;
  font-display: fallback;
  src: local('SourceHanSansCN-Normal'), url('../font/SourceHanSansCN-Normal.otf');
}
// @font-face {
//   font-family: Source-CN-Regular;
//   font-display: fallback;
//   src: local('SourceHanSansCN-Regular'), url('../font/SourceHanSansCN-Regular.otf');
// }

.div-icon {
  padding: 0;
  background-color: transparent;
  pointer-events: none;
}

.mars-spot {
  background-image: url('img/lbl-circle.png');
  position: absolute;
  background-repeat: no-repeat;
  user-select: none;
  z-index: 100000;
  width: 70px;
  height: 44px;
  background-size: cover;
  bottom: -80px;
  left: -30px;
  cursor: default;
}

.mars-spot:hover {
  background-image: url('img/lbl-circle2.png');
}

.mars-spot:hover .mars-spot-board {
  background-image: url('img/lbl-extent2.png');
}

.mars-spot:hover .mars-spot-line {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot:hover .mars-spot-line:before {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot:hover .mars-spot-line:after {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-board {
  background-image: url('img/lbl-extent.png');
  top: -165px;
  transform: translateX(-30%);
  width: 180px;
  height: 52px;
  padding: 15px 0;
  background-position: 50%;
  background-size: contain;
  cursor: pointer;
  position: absolute;
  background-repeat: no-repeat;
}

.mars-spot-board:hover {
  background-image: url('img/lbl-extent2.png');
}

.mars-spot-board:hover + .mars-spot-line {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-board:hover + .mars-spot-line:before {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-board:hover + .mars-spot-line:after {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-line {
  position: absolute;
  top: -108px;
  left: 35px;
  width: 2px;
  height: 140px;
  background: linear-gradient(180deg, #38c9ff, transparent);
}

.mars-spot-line:hover {
  background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-line:before {
  height: 40%;
  left: 6px;
  animation: rise 3s linear 2s infinite;
  -webkit-animation: rise 3s linear 2s infinite;
  content: '';
  display: block;
  width: 1px;
  background: linear-gradient(180deg, #38c9ff, transparent);
  position: absolute;
  cursor: pointer;
}

.mars-spot-line:after {
  content: '';
  display: block;
  width: 1px;
  background: linear-gradient(180deg, #38c9ff, transparent);
  position: absolute;
  cursor: pointer;
  height: 60%;
  left: -5px;
  animation: rise 3s linear infinite;
  -webkit-animation: rise 3s linear infinite;
}
.mars-spot-board h5 {
  width: 100%;
  text-align: center;
  line-height: 24px;
  color: #beedff;
  font-size: 16px;
  margin: 0;
}

.animation-spaceInDown {
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: spaceInDown;
}

@keyframes rise {
  0% {
    opacity: 0.1;
    transform: translate(0, 100%);
  }
  5% {
    opacity: 0.3;
    transform: translate(0, 95%);
  }
  10% {
    opacity: 0.6;
    transform: translate(0, 90%);
  }
  15% {
    opacity: 1;
    transform: translate(0, 85%);
  }
  75% {
    opacity: 1;
    transform: translate(0, 25%);
  }
  80% {
    opacity: 0.7;
    transform: translate(0, 20%);
  }
  90% {
    opacity: 0.3;
    transform: translate(0, 10%);
  }
  95% {
    opacity: 0.2;
    transform: translate(0, 5%);
  }
  100% {
    opacity: 0.1;
    transform: translate(0, 0);
  }
}

.mars-four-color {
  width: 86px;
  position: relative;
  top: -68px;
  left: -37px;
  cursor: pointer;
}

.mars-four-color .four-color_bg {
  position: absolute;
}

.mars-four-color .four-color_name {
  width: 100%;
  position: absolute;
  top: 8px;
  text-align: center;
}

.mars3d-animation {
  animation: cameraMove 1s linear infinite alternate;
  -webkit-animation: cameraMove 1s linear infinite alternate;
}

@keyframes cameraMove {
  from {
    top: -100px;
  }

  to {
    top: -68px;
  }
}

@-webkit-keyframes cameraMove {
  from {
    top: -100px;
  }

  to {
    top: -68px;
  }
}

.marsBlueGradientPnl {
  text-align: center;
  padding: 5px 30px;
  margin: 0;
  color: #fff;
  background: linear-gradient(rgb(7 10 203 / 75%), rgb(16 238 220));
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  max-height: 130px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
}

.marsBlueGradientPnl:after {
  content: '';
  position: absolute;
  bottom: -60px;
  left: calc(50% - 3px);
  display: block;
  width: 3px;
  height: 60px;
  border-right: 3px solid #2bcdbb;
}

.marsBlueGradientPnl-highlight {
  border: 2px solid yellow;
}
.marsBlueGradientPnl-highlight:after {
  border-right: 3px solid yellow;
}

.mars3d-camera-content {
  height: 30px;
}
.mars3d-camera-img {
  width: 30px;
  height: 30px;
  animation: cameraMove 1s linear infinite alternate;
  -webkit-animation: cameraMove 1s linear infinite alternate;
}
@keyframes cameraMove {
  from {
    margin-top: 20px;
  }
  to {
    margin-top: 0px;
  }
}
@-webkit-keyframes cameraMove {
  from {
    margin-top: 20px;
  }
  to {
    margin-top: 0px;
  }
}
.mars3d-camera-line {
  height: 120px;
  width: 5px;
  margin-top: 20px;
  border-left: 3px dashed #5b8fee;
  margin-left: calc(50% - 1px);
}
.mars3d-camera-point {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  margin-left: calc(50% - 3px);
  background-color: #5b8fee;
}

.marsBlackPanel {
  min-width: 90px;
  min-height: 35px;
  position: absolute;
  left: 16px;
  bottom: 31px;
  cursor: default;
  border-radius: 4px;
  opacity: 0.96;
  border: 1px solid #14171c;
  box-shadow: 0px 2px 21px 0px rgba(33, 34, 39, 0.55);
  border-radius: 4px;
  box-sizing: border-box;
  background: linear-gradient(0deg, #1e202a 0%, #0d1013 100%);
}

.marsBlackPanel::before {
  content: '';
  width: calc(100% + 22px);
  height: 39px;
  position: absolute;
  bottom: -39px;
  left: -22px;
  background: url('img/popupLbl.png') 0px 0px no-repeat;
  background-position: 0px 0px;
}

.marsBlackPanel-text {
  width: 100%;
  height: 100%;
  min-height: 33px;
  text-align: center;
  padding: 10px 5px 0 5px;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  border: 1px solid #ffffff4f;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap;
}

.marsBlackPanel-highlight {
  border: 2px solid yellow;
}

@keyframes spaceInDown {
  0% {
    opacity: 0;
    transform-origin: 0% 100%;
    transform: scale(0.2) translate(0, 200%);
  }
  100% {
    opacity: 1;
    transform-origin: 0% 100%;
    transform: scale(1) translate(0, 0);
  }
}

.mars3d-animation-point,
.mars3d-animation-point:after,
.mars3d-animation-point:before,
.mars3d-animation-point p,
.mars3d-animation-point p:after,
.mars3d-animation-point p:before {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
.mars3d-animation-point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid hsla(0, 0%, 100%, 0.5);
  cursor: pointer;
  color: #0ff;
  background: currentColor;
  z-index: 3;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  box-shadow:
    0 0 2em currentColor,
    0 0 0.5em currentColor;
  position: absolute;
}
.mars3d-animation-point .mars3d-animation-point-lbl {
  position: absolute;
  transform: translate(-50%, -120%);
  left: 50%;
  font-size: 16px;
  width: fit-content;
  white-space: nowrap;
}
.mars3d-animation-point p {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-animation: mars3d-animation-point-mapAni 2s ease infinite;
  -moz-animation: mars3d-animation-point-mapAni 2s ease infinite;
  -o-animation: mars3d-animation-point-mapAni 2s ease infinite;
  -ms-animation: mars3d-animation-point-mapAni 2s ease infinite;
  animation: mars3d-animation-point-mapAni 2s ease infinite;
}
.mars3d-animation-point .mapError {
  color: red;
}
.mars3d-animation-point .mapWarn {
  color: #b5a603;
}
.mars3d-animation-point .mapSuccess {
  color: #239233;
}
.mars3d-animation-point .mapOrange {
  color: #8c4d34;
}
.mars3d-animation-point:after,
.mars3d-animation-point:before,
.mars3d-animation-point p:after,
.mars3d-animation-point p:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  top: 50%;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.mars3d-animation-point:after,
.mars3d-animation-point:before {
  border: 1px solid;
  -webkit-animation: mars3d-animation-point-mapAni 1s ease infinite;
  -moz-animation: mars3d-animation-point-mapAni 1s ease infinite;
  -o-animation: mars3d-animation-point-mapAni 1s ease infinite;
  -ms-animation: mars3d-animation-point-mapAni 1s ease infinite;
  animation: mars3d-animation-point-mapAni 1s ease infinite;
}
.mars3d-animation-point p:before {
  border: 1px solid;
}
@-webkit-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-moz-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-o-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-ms-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.mars3d-divBoderLabel {
  position: absolute;
  left: 0px;
  bottom: 0px;
  cursor: pointer;
  animation-name: mars3d-divBoderLabel-animation;
  --text-left-position: -75px;
}
@keyframes mars3d-divBoderLabel-animation {
  0%,
  100% {
    clip: rect(0px, 177px, 2px, 0px);
  }
  25% {
    clip: rect(0px, 2px, 40px, 0px);
  }
  50% {
    clip: rect(38px, 177px, 177px, 0px);
  }
  75% {
    clip: rect(0px, 177px, 40px, 175px);
  }
}
.mars3d-divBoderLabel-boder {
  width: 162px;
  height: 30px;
  margin: auto;
  color: #15d1f2;
  box-shadow: inset 0 0 0 1px rgba(21, 209, 242, 0.5);
}
.mars3d-divBoderLabel-text {
  color: white;
  background-color: rgba(49, 49, 49, 0.2);
  font-size: 15px;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  font-weight: bolder;
  user-select: none;
  cursor: pointer;
}
.mars3d-divBoderLabel-boder,
.mars3d-divBoderLabel-boder::before,
.mars3d-divBoderLabel-boder::after {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.mars3d-divBoderLabel-boder::before,
.mars3d-divBoderLabel-boder::after {
  content: '';
  margin: -5%;
  box-shadow: inset 0 0 0 2px;
  animation: mars3d-divBoderLabel-animation 8s linear infinite;
}
.mars3d-divBoderLabel-boder::before {
  animation-delay: -4s;
}

.mars3d-divUpLabel {
  text-align: center;
  background: transparent;
  color: white;
  display: block;
  box-sizing: border-box;
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: mars3d-divUpLabel-tinUpIn;
}
.mars3d-divUpLabel-text {
  writing-mode: vertical-lr;
  font-size: 16px;
  letter-spacing: 4px;
}
.mars3d-divUpLabel-line {
  display: block;
  height: 100px;
  width: 1.5px;
  margin-left: calc(50% - 1px);
  margin-top: 3px;
  background-color: white;
}
@keyframes mars3d-divUpLabel-tinUpIn {
  0% {
    opacity: 0;
    transform: scale(1, 1) translateY(-900%);
  }
  50%,
  70%,
  90% {
    opacity: 1;
    transform: scale(1.1, 1.1) translateY(0);
  }
  100%,
  60%,
  80% {
    opacity: 1;
    transform: scale(1, 1) translateY(0);
  }
}

.marsGreenGradientPnl {
  width: 100px;
  text-align: center;
  background-image: linear-gradient(to right, #565d39, #00ffc3);
  position: relative;
  left: -1px;
  bottom: 29px;
  cursor: default;
  padding: 5px;
  border: 1px solid #9c9944e8;
}

.marsGreenGradientPnl:hover {
  border: 1px solid rgb(9, 255, 0);
}

.marsGreenGradientPnl::before {
  position: absolute;
  content: '';
  left: 50%;
  bottom: -30px;
  height: 30px;
  border-left: 2px dashed #c5e22770;
}

.marsGreenGradientPnl-highlight {
  border: 2px solid yellow;
}

.marsGreenGradientPnl-highlight::before {
  border-left: 2px dashed yellow !important;
}

.infoStyle {
  margin-top: 10px;
  margin-left: 10px;
  color: white;
  line-height: 2;
}
.dynamicPopup {
  position: fixed;
  width: 300px;
  height: 240px;
  /*重要*/
  user-select: none;
  /*禁止选中*/
  pointer-events: none;
  /* z-index: 99999; */
  /* background-color: red; */
}

.dynamicPopup > .line {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 37px;
  background: url('@/assets/img/line2.png');
  background-repeat: no-repeat;
  background-size: 142.5px 100%;
  animation: goLine 1.5s forwards;
}

@keyframes goLine {
  from {
    width: 0;
  }

  to {
    width: 142.5px;
  }
}

.dynamicPopup > .main {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  background: url('@/assets/img/out.png') no-repeat;
  background-size: 100% 100%;
  animation: goDynamicLayer 0.5s forwards;
  animation-delay: 1.5s;
  margin-left: 142.5px;
  transform: translateY(35%);
}

.dynamicPopup > .main > .border1 {
  position: absolute;
  border: 1px solid #50dfbc;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  background: #0a091c5d;
}

.dynamicPopup > .main > .border1 > .border2 {
  position: absolute;
  border: 1px solid #50dfbc;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
}

@keyframes goDynamicLayer {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.left-drawer {
  position: absolute;
  left: 20px;

  top: 81px;
  width: 380px;
  height: calc(100% - 100px);
  z-index: 1;
  overflow-x: hidden;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 0;
  }
}

.right-drawer {
  position: absolute;
  right: 20px;
  top: 85px;
  z-index: 1;
  width: 380px;
  height: calc(100% - 100px);
  overflow-x: hidden;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 0;
  }
}
.custom-select {
  background-color: var(--transparent) !important;
  box-shadow: none !important;
  width: 100%;
  border-radius: 4px;
  .el-select__wrapper {
    --el-text-color-regular: #ffffff;
    border: 1px solid rgba(21, 238, 191, 0.4047);
    background: linear-gradient(90deg, #22cea9 2%, rgba(15, 205, 164, 0.06) 100%);
    box-shadow: none !important;
    color: #ffffff;
  }
  .el-tag.el-tag--info {
    --el-tag-text-color: rgba(0, 0, 0, 0.9);
  }

  // 选择框
  .slct_popper {
    background: rgba(#64b5a5, 1);
    border: none !important;
    box-shadow: 0px 0 0 0 rgba(0, 0, 0, 0.2);

    .el-select-dropdown__item {
      color: #8dd9dc;
    }

    .el-select-dropdown__item.hover {
      background-color: #64b5a5;
    }

    .el-select-dropdown__item:hover {
      background-color: rgba(#70d3bf, 1);
    }

    .el-popper__arrow::before {
      background: transparent;
      border: none;
    }

    .el-select-dropdown__item.selected {
      background-color: rgba(#70d3bf, 1);
    }

    .el-select-dropdown__item.selected.hover {
      background-color: rgba(#70d3bf, 1);
    }

    .el-popper__arrow {
      opacity: 0;
    }
  }
}

.no-data {
  color: #fff;
  text-align: center;
  margin-top: 60px;
  opacity: 0.7;
}

.operate-btns {
  display: flex;
  padding: 15px;
  justify-content: center;
  .operate-btn {
    margin: 0 15px;
    // color: white;
    padding: 0 25px;
  }
}

.pagibox {
  border-top: 1px solid rgba(127, 199, 244, 0.34);
  display: flex;
  height: 66px;
  align-items: center;

  .custom-pagi-card {
    position: absolute;
    right: 20px;
    margin-right: 20px;

    .custom-pagis {
      .btn-prev,
      .btn-next {
        width: 34px;
        height: 34px;
        border: 1px solid rgba(65, 110, 138, 0.8);
        background-color: rgba(65, 110, 138, 0.8);
        border-radius: 4px;
        color: #88b7d8;
      }

      .el-pager {
        .number,
        .more {
          width: 34px;
          height: 34px;
          border-radius: 4px;
          border: 1px solid rgba(65, 110, 138, 0.8);
          background-color: rgba(65, 110, 138, 0.8);
          font-size: 14px;
          font-family: Source-CN-Regular;
          font-weight: 400;
          color: #88b7d8;
          letter-spacing: 0.56px;
        }

        .number:hover,
        .number.is-active {
          background: #4bb0ee;
          color: #ffffff;
        }
      }

      .btn-prev:hover,
      .btn-next:hover {
        background-color: #4bb0ee;
        border: 1px solid #4bb0ee;
        color: #fff;
      }

      .btn-prev:disabled,
      .btn-next:disabled {
        border: 1px solid #ebebeb;
        color: #fff;
        background: transparent;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
  }
}
