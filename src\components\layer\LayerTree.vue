<template>
  <div class="layer-manage">
    <el-tree
      :data="layerdata"
      :props="defaultProps"
      @node-click="handleNodeClick"
      node-key="id"
      default-expand-all
      class="layer-tree custom-tree-default"
      highlight-current
      ref="treeRef"
    >
      <template v-slot="{ node, data }">
        <div
          class="custom-tree-node"
          @mouseenter="currNode = node.id"
          @mouseleave="currNode = null"
        >
          <el-tooltip placement="top" :show-after="500">
            <span
              class="layer-name"
              tabindex="-1"
              @blur="() => onBlur(data)"
              >{{ data.title }}</span
            >
            <template #content>
              {{ data.title }}
            </template>
          </el-tooltip>

          <div class="layer-operate">
            <el-switch
              class="layer-switch custom-switch"
              v-model="data.show"
              :active-value="true"
              :inactive-value="false"
              @click.stop="() => {}"
              @change="(value: any) => setLayerVisible(data, node, value)"
            >
            </el-switch>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, onUnmounted, type PropType, reactive, type Ref, ref } from 'vue'
import type Node from 'element-plus/es/components/tree/src/model/node'
import { App } from '@/base/App'
import { type LayerItem } from '@/base/layer/type/LayerItem'
import { Subscription } from 'rxjs'
let defaultProps: any = reactive({
  children: 'children',
  label: 'title',
  class: 'custom-class'
})
const props = defineProps({
  onTreeClick: Function
})

const emits = defineEmits(['onLoseFocus', 'onDblClick'])
let currNode: Ref<any> = ref()
let layerdata = ref([] as LayerItem[])
const treeRef = ref()
const onBlur = (data: any) => {
  setTimeout(() => {
    let key = treeRef.value.getCurrentKey()
    if (key === data.id) {
      treeRef.value.setCurrentKey(null)
      emits('onLoseFocus', key)
    }
  }, 200)
}
let currParentId: number | null = null
const setLayerVisible = (data: any, node: Node, visible: boolean) => {
  checkedChange(data, node, 'show', true, false)
  App.getInstance().LayerManager.setLayerVisible(data.id, visible)
}

let nodeCount = 0
let preNodeId: any = null
let curNodeId = null
let nodeTimer: any = null
const handleNodeClick = (data: any, node: any, prop: any) => {
  nodeCount++
  if (preNodeId && nodeCount >= 2) {
    curNodeId = data.id
    nodeCount = 0
    if (curNodeId == preNodeId) {
      //第一次点击的节点和第二次点击的节点id相同
      emits('onDblClick', data)
      App.getInstance().LayerManager.flyToLayer(curNodeId!)
      curNodeId = null
      preNodeId = null
      return
    }
  }
  preNodeId = data.id
  nodeTimer = setTimeout(() => {
    //300ms内没有第二次点击就把第一次点击的清空
    if (nodeCount === 1) {
      // if (props.onTreeClick) {
      //   let layer = LayerManager.Manager().findLayerById(data.id);
      //   props.onTreeClick(layer);
      // }
    }
    preNodeId = null
    nodeCount = 0
  }, 300)
}

const parentVisible = (node: Node, prop: string, trueValue: any, falseValue: any) => {
  const parent = node.parent
  if (parent?.data.children) {
    let res = parent.data.children.find((layer: any) => layer[prop] === trueValue)
    parent.data[prop] = res ? trueValue : falseValue
    parentVisible(parent, prop, trueValue, falseValue)
  }
}
const childrenVisible = (data: any, prop: string, value: any) => {
  data.children?.forEach((layer: any) => {
    layer[prop] = value
    childrenVisible(layer, prop, value)
  })
}
/**
 * 控制选中状态
 */
const checkedChange = (data: any, node: any, prop: string, trueValue: any, falseValue: any) => {
  if (data) {
    parentVisible(node, prop, trueValue, falseValue)
    // 控制父级显示
    // let parent: LayerItem = LayerManager.Manager().findLayerByIdRecursion(data.parentId, layerdata.value as any) as any;
    // if (parent) {
    //   //let res = parent.children.find(layer => layer.visible === 1)
    //   //parent.visible = !!res ? 1 : 0;
    //   parent.visible = data.visible
    //   // let layer: LayerItem = LayerManager.Manager().findLayerById(parent.id);
    //   // LayerManager.Manager().setLayerVisible(layer, parent.visible === 1);
    // }
    // 控制子级显示
    childrenVisible(data, prop, data[prop])
  }
}

let subscription: Subscription | null = null
// 加载图层
onMounted(() => {
  const layerManager = App.getInstance().LayerManager
  if (layerManager) {
    subscription = layerManager.layerChanged.subscribe((layers: any) => {
      layerdata.value = layers ?? []
    })
  }
})
onUnmounted(() => {
  if (subscription) {
    subscription.unsubscribe()
  }
})
</script>
<style lang="scss" scoped>
.layer-manage {
  width: inherit;
  height: 476px;
  padding: 20px;
  background: transparent;
  pointer-events: auto;
}

.layer-name {
  line-height: 32px;
}

.layer-name-invalid {
  line-height: 32px;
  color: #f00;
}

.layer-icon-buttom {
  width: 16px;
  height: 16px;
  border: 1px solid #059af2;
  border-radius: 8px;
  color: #059af2;
  margin: 0 5px;
  vertical-align: middle;
}

.layer-operate {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  // line-height: 32px;
}

.custom-tree-node {
  display: flex;
  width: 100%;
  height: 100%;
  // justify-content: space-between;
  align-items: center;
  position: relative;
}

.layer-switch {
  margin-left: 10px;
}
</style>
