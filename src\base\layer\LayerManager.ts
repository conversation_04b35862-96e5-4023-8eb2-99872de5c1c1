import { BehaviorSubject } from "rxjs";
import { BaseLayer } from "./layers/BaseLayer";
import { LayerGroup } from "./layers/LayerGroup";
import { type LayerItem } from "./type/LayerItem";

export class LayerManager {
  private _viewer: BC.Viewer;
  private _layers: any;
  layerChanged = new BehaviorSubject<LayerItem[]>([]);
  constructor(viewer: BC.Viewer) {
    this._viewer = viewer;
  }

  addLayers(config: any) {
    // 移除已加载图层
    this._layers && this._layers.remove();

    this._layers = new LayerGroup({
      children: config,
    });
    this._layers.addTo(this._viewer);
    // 发送图层更改事件
    this.layerChanged.next(this.getLayerTree());
  }
  /**
   * 根据图层查找id
   * @param layerId 图层id
   * @returns
   */
  findLayerById(layerId: string): BaseLayer | null {
    return this._layers.findLayer(layerId);
  }

  /**
   * 根据条件查找图层
   * @param predicate 图层查询条件表达式 
   * @returns 
   */
  find(predicate: (layer: BaseLayer) => boolean){
    return this._layers.findLayers(predicate);
  }


  /**
   * 定位到图层
   * @param layerId 图层id
   */
  flyToLayer(layerId: string) {
    const layer = this.findLayerById(layerId);
    layer && layer.flyTo();
  }

  /**
   * 获取图层树结构
   */
  getLayerTree() {
    const layerTree = this._layers.getLayerTree();
    return layerTree.children ?? [];
  }

  setLayerVisible(layerId: string, show: boolean) {
    const layer = this.findLayerById(layerId);
    if (layer) {
      layer.show = show;
    }
  }


}
