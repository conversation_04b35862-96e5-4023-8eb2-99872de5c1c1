<!--
 * @Author: xiao
 * @Date: 2024-07-24 09:52:01
 * @LastEditors: xiao
 * @LastEditTime: 2024-07-24 15:50:53
 * @Description: 管线管理-左侧列表
-->
<template>
  <base-card
    :title="properties.title"
    :width="properties.width"
    :initial-value="properties.initialValue"
    :uuid="uuid"
  >
    <div class="pipe-list-main">
      <div class="query-form">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="管线名称">
              <el-input
                v-model="queryForm.name"
                type="text"
                placeholder="请输入"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="管线编码">
              <el-input
                v-model="queryForm.code"
                type="text"
                placeholder="请输入"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运行状态">
              <el-select
                v-model="queryForm.currStatus"
                placeholder="请选择"
                class="full-width-input"
                clearable
                popper-class="custom-select-popper"
              >
                <el-option
                  v-for="item in runningState"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="管线类型">
              <el-select
                v-model="queryForm.type"
                placeholder="请选择"
                class="full-width-input"
                clearable
                popper-class="custom-select-popper"
              >
                <el-option
                  v-for="item in pipelineType"
                  :key="item.id"
                  :label="item.dictLabel"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col class="query-btns" :span="4">
            <el-button color="#4bb0ee" @click="queryData" style="color: #fff">查询</el-button>
            <el-button color="#498baa6b" @click="reset" style="color: #fff">重置</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="table-box custom-scroll">
        <!-- <el-table class="custom-table" v-loading="listLoading" :data="list" height="100%"> -->
        <el-table class="custom-pipeline_list" :data="filterList" height="100%">
          <el-table-column type="index" label="序号" width="70" min-width="50" />
          <el-table-column prop="name" label="管线名称" min-width="80" show-overflow-tooltip />
          <el-table-column prop="code" label="管线编码" show-overflow-tooltip />
          <el-table-column prop="type" label="管线类型" width="90" show-overflow-tooltip >
            <template #default="{ row }">
              <span>{{ getType(row.type) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="length" label="管线长度(m)" show-overflow-tooltip />
          <el-table-column prop="contactPerson" label="联系人" width="90" show-overflow-tooltip />
          <el-table-column prop="telephone" label="联系电话" width="100" show-overflow-tooltip />
          <el-table-column prop="currStatus" label="运行状态" width="80" show-overflow-tooltip >
            <template #default="{ row }">
              <span>{{ getStatusTxt(row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="入廊管线单位" show-overflow-tooltip />
          <el-table-column prop="material" width="100" label="管线材质">
            <template #default="{ row }">
              <div v-if="testHex(row.material)" class="material-option-img" :style="{ background: row.material }"></div>
              <img
                v-else-if="!testHex(row.material)"
                class="material-option-img"
                :src="getImages(`pipeline/${row.material}`)"
                alt=""
              />
            </template>
          </el-table-column>
          <el-table-column label="入廊分区" show-overflow-tooltip>
            <template #default="{ row }">
              {{ getCarbinText(row) }}
            </template>
          </el-table-column>
          <el-table-column label="入廊位置" show-overflow-tooltip>
            <template #default="{ row }">
              {{ getPipeLocationText(row) }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" min-width="125">
          <template #default="{ row }">
            <el-button
              link
              class="custom-link-btn"
              v-loading="btnLoading"
              @click="checkHandler(row)"
              >详情</el-button
            >
            <el-button class="custom-link-btn" link v-loading="btnLoading" @click="editHandler(row)"
              >编辑</el-button
            >

            <el-button
              class="custom-link-btn"
              link
              v-loading="btnLoading"
              @click="deleteHandler(row)"
              >删除</el-button
            >
          </template>
        </el-table-column> -->
        </el-table>
      </div>
      <!-- <div class="pagibox box-border p-x-5"> -->
        <!-- <div class="pagitotal">共{{ tableSize }}条数据</div> -->
        <!-- <pagination
          class="custom-pagi-card"
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          @pagination="getList"
        ></pagination> -->
      <!-- </div> -->
    </div>
  </base-card>
</template>

<script lang="ts" setup>
import { cabinAreas, cabins, runningState, location, locationCols, locationRows } from '@/constant/baseData'
import { getImages } from '@/utils/getImages'
const props = defineProps<{
  properties: Pipeline.CardProperties
  uuid: string
}>()

const cardModule = useModuleCard()

const pipelineStore = usePipeline()
const { pipelineList, pipelineType } = storeToRefs(pipelineStore)
// const { getPipelineList, getPipeLineType } = pipelineStore
const getType = (type: string) => {
  return pipelineType.value.find((item: any) => item.id === type)?.dictLabel
}
const initQueryForm = () => {
  return {
    name: '',
    code: '',
    currStatus: '',
    type: '',
    pageNum: 1,
    pageSize: 10
  }
}
const queryForm = ref(initQueryForm())
const getCarbinText = (row: any) => {
  const start = row.startPartition
  const end = row.endPartition
  const location = row.zoningLocation
  const startText = cabinAreas.find((item: any) => item.value === start)?.label
  const endText = cabinAreas.find((item: any) => item.value === end)?.label
  const carbinText: any = cabins.find((item: any) => item.value === location)?.label
  return (carbinText + startText + '至' + endText)
}
const getPipeLocationText = (row: any) => {
  const loc = row.entranceLocation;
  const rowNum = row.rowNum;
  const colNum = row.number;
  const locText: any = location.find((item: any) => item.value === loc)?.label
  const rowText = locationRows.find((item: any) => item.value === rowNum)?.label
  const colText = locationCols.find((item: any) => item.value === colNum)?.label
  return (locText + rowText + colText)
}
const list = ref([
  {
    name: '1分区2舱室ql管线',
    code: 'AGL1code2ql',
    type: '电力管线',
    length: '100m',
    contactPerson: '李四',
    telephone: '13800000000',
    currStatus: '正常',
    unit: '条',
    material: '合金',
    zoningLocation: '综合仓',
    startPartition: '1分区',
    endPartition: '2分区',
    entranceLocation: '右侧',
    rowNum: '第一排',
    number: '第二根'
  }
])
const total = list.value.length
const getList = () => {}

// const openCard = (obj?: { [key: string]: any }) => {
//   cardModule.changeCard({
//     name: 'PipeLineCard',
//     properties: { title: '管线新增', width: '36%', initialValue: { x: 500, y: 160 }, ...obj }
//   })
// }
const getStatusTxt = (val: number): string => {
  let txt = ''
  switch (val) {
    case 0:
      txt = '正常'
      break
    case 1:
      txt = '停用'
      break
    case 2:
      txt = '故障'
      break
    default:
      break
  }
  return txt
}
const testHex = (hex: string) => {
  const regex = /^#[0-9A-F]{6}$/i
  return regex.test(hex)
}

// const checkHandler = (row: any) => {
//   openCard({ disabled: true, id: row.id })
// }

// const editHandler = (row: any) => {
//   openCard({ id: row.id })
// }

// const btnLoading = ref(false)
// const deleteHandler = (row: any) => {
//   useDelMessageBox(() => {
//     btnLoading.value = true
//     deletePipeline(row.id)
//       .then((res) => {
//         if (res.code == 200) {
//           ElMessage.success(res.message)
//           getPipelineList()
//           removePipeLines([row])
//         }
//       })
//       .catch((err) => console.error(err, 'delete pipeline error'))
//       .finally(() => (btnLoading.value = false))
//   })
// }
// const removePipeLines = (items: any) => {
//   const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === '管线')
//   if (layers.length > 0) {
//     const layer: any = layers[0]
//     layer.removePipeLineeByItems(items)
//   }
// }

// 查询管线列表
// getPipelineList()
const filterList: Ref<any> = ref([])
onMounted(async () => {
  filterList.value = pipelineList.value;
})
const reset = () => {
  filterList.value = pipelineList.value;
  queryForm.value = initQueryForm()
}
const queryData = () => {
  const { name, code, currStatus, type } = queryForm.value
  filterList.value = pipelineList.value.filter((item: any) => {
    return (
      (name === '' || item.name.includes(name)) &&
      (code === '' || item.code.includes(code)) &&
      (!currStatus || currStatus === '' || item.currStatus === currStatus) &&
      (!type || type === '' || item.type === type)
    )
  })
}
</script>

<style lang="scss" scoped>
.pipe-list-main {
  box-sizing: border-box;
  padding: 16px;
}
.table-box {
  margin-top: 16px;
  height: 600px;
}
.mat-img {
  width: 140px;
  height: 16px;
}
.query-btns {
  .el-button {
    width: 80px;
    height: 28px;
  }
}
.material-option-img {
  position: absolute;
  // left: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 16px;
}
</style>
