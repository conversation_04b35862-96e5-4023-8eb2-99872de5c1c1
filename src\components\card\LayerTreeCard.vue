
<template>
  <BaseCard :width="'360px'" :title="'图层管理'" :closed-method="close">
    <LayerTree></LayerTree>
  </BaseCard>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import BaseCard from "./BaseCard.vue";
import LayerTree from '@/components/layer/LayerTree.vue'
onUnmounted(() => {

});
onMounted(async () => {
});
const close = () => {}
</script>

<style lang="scss" scoped>

</style>
