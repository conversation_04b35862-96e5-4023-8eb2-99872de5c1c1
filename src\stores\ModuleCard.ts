/*
 * @Author: xiao
 * @Date: 2024-04-03 09:37:39
 * @LastEditors: xiao
 * @LastEditTime: 2024-07-24 10:48:46
 * @Description:
 */
import { defineStore, storeToRefs } from 'pinia'
import { ref, h } from 'vue'
import MeasureCard from '@/components/card/analysis/MeasureCard.vue'
import LayerTreeCard from '@/components/card/LayerTreeCard.vue'
import ClipAnalysisCard from '@/components/card/analysis/ClipAnalysisCard.vue'
import ViewShedCard from '@/components/card/analysis/ViewShedCard.vue'
import SlightLineCard from '@/components/card/analysis/SlightLineCard.vue'
import PipeLineCard from '../components/card/PipeLineCard.vue'
import PipeListCard from '@/components/card/PipeListCard.vue'
import { uuid2 } from '@/base/utils/CommonUtils'

export const useIsAMap = defineStore('IsAMap', () => {
  const isAMap = ref(true)
  function change(bool: boolean) {
    isAMap.value = bool
  }
  return {
    change,
    isAMap
  }
})

//三维分析
const components: any = {
  MeasureCard: MeasureCard,
  ClipAnalysisCard: ClipAnalysisCard,
  LayerTreeCard: LayerTreeCard,
  ViewShedCard: ViewShedCard,
  SlightLineCard: SlightLineCard,
  PipeLineCard: PipeLineCard,
  PipeListCard: PipeListCard
}

export const useModuleCard = defineStore('moduleCard', () => {
  const mainComponents: any = ref([])
  const command = ref('')
  function changeCard(card: any) {
    //每次都先清空弹框
    mainComponents.value = []
    const sameNameCard = mainComponents.value.find((c: any) => c.name === card.name)
    if (sameNameCard) {
      mainComponents.value.splice(
        mainComponents.value.findIndex((item: any) => item.uuid === sameNameCard.uuid),
        1
      )
    }
    const componentIns = h(components[card.name])
    mainComponents.value.push({
      component: componentIns,
      uuid: uuid2(),
      name: card.name,
      properties: card.properties
    })
  }
  function closeCard(uuid: string) {
    mainComponents.value.splice(
      mainComponents.value.findIndex((item: any) => item.uuid === uuid),
      1
    )
  }
  function clearCard() {
    mainComponents.value = []
  }
  function refreshList(name: string) {
    command.value = name
  }
  return {
    mainComponents,
    command,
    changeCard,
    closeCard,
    clearCard,
    refreshList
  }
})
