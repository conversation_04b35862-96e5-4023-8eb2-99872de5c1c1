<template>
  <div class="device-title">
    <img :src="icon" alt="" />
    <span>{{ title }}</span>
  </div>
</template>

<script setup lang="ts">
import { getImages } from '@/utils/getImages'

withDefaults(defineProps<{ title: string }>(), {})

const icon = getImages('deviceLocation/title-icon.png')
</script>

<style scoped lang="scss">
.device-title {
  display: flex;
  align-items: center;
  font: 500 14px Source-CN-Medium;
  color: #2bcaff;
  > span {
    margin-left: 6px;
  }
}
</style>
