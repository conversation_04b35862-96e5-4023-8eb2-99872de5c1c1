import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import MainView from '@/views/MainView.vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: MainView,
      redirect: "/sceneview",
      children: [
        {
          path: "/sceneview",
          name: "SceneView",
          component: () => import("@/views/SceneView.vue"),
        },
        
      ],
    }
  ]
})

export default router
