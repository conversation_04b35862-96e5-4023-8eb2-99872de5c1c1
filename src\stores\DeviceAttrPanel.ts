import { type Ref } from 'vue'
import { defineStore } from 'pinia'
import { getDataSource, setHeight } from '@/base/utils/CesiumUtils'
import { App } from '@/base/App'
import { getAlarmInfoByCode, getDeviceByCode } from '@/api/device_server'
import { getDeviceStatusLabel } from '@/base/utils/CommonUtils'
/**
 * 设备信息弹框
 */
const addBillboardDiv = async (deviceCode: string, extraAttr?: string[]) => {
  const { Cesium } = BC.Namespace
  const { code, data } = await getDeviceByCode(deviceCode)
  
  if (code !== '00000') return
  let id = ''
  
  
  const dataSource = getDataSource('device_attr_panel', App.getInstance().getViewer())
  //todo: 根据设备id获取设备属性
  const texts = []
  let name: string = data.name
  if(data.name.indexOf('#') !== -1) {
    name = data.name.split('#')[1]
  }
  texts.push(`设备编码：${data.code}`)
  texts.push(`设备名称：${name}`)
  texts.push(`设备类型：${data.deviceTypeName}`)
  texts.push(`设备状态：${getDeviceStatusLabel(data.deviceStatus)}`)
  if(data.deviceStatus === 'WARN') {
    const result = await getAlarmInfoByCode({
      current: 1,
      query: {
        equipmentCode: deviceCode,
      },
      size: 10
    })
    if(result.code === '00000' && result.data.records.length > 0) {
      const alarmInfo = result.data.records[0];
      texts.push(`告警等级：${alarmInfo.alarmLevel}`)
      texts.push(`告警描述：${alarmInfo.alarmDescription}`)
      texts.push(`告警时间：${alarmInfo.alarmTime}`)
    }
  }
  extraAttr && texts.push(...extraAttr)
  const sphere = useCesiumStructure().getSphere(data.code)
  console.log(sphere, data)
  if (!sphere) {
    console.log('当前设备无经纬度', data)
    return
  }
  let center = new Cesium.Cartesian3(sphere[0], sphere[1], sphere[2])
  //cartesian转经纬度
  const cartographic = Cesium.Cartographic.fromCartesian(center)
  const lat = Cesium.Math.toDegrees(cartographic.latitude)
  const lng = Cesium.Math.toDegrees(cartographic.longitude)
  const height = cartographic.height
  center = setHeight(center, height + 7)
  createCusbilimg2(texts, data.deviceStatus, (result: any) => {
    const entity = dataSource.entities.add({
      name: '设备信息点22',
      //todo: 设备位置
      position: center,
      billboard: {
        image: result.img,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        // width: 200,
        // height: 120,
        scale: 1,
        verticalOrigin:
          result.type === 'WARN' ? Cesium.VerticalOrigin.CENTER : Cesium.VerticalOrigin.TOP,
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 12),
        // color:new Cesium.Color(0,22/255,57/255)
        pixelOffset: new window.Cesium.Cartesian2(result.offsetx, result.offsety)
      }
    })
    id = entity.id
  })

  return id
}
/**
 * 生成canvas弹框
 * @param texts
 * @param type
 * @param callback
 */
const createCusbilimg2 = (texts: any, type: string, callback: Function) => {
  const myImage = new Image()
  myImage.crossOrigin = 'Anonymous'
  console.log(type)
  //旋转角度如果大于零，线在上
  if (type === 'WARN') {
    myImage.src = 'img/popup_bg23.png'
  } else if (type === 'NORMAL') {
    myImage.src = 'img/popup_bg13.png'
  }

  const width = 305,
    height = 329
  let canvas: any = document.createElement('canvas')
  const context: any = canvas.getContext('2d')

  myImage.onload = function () {
    canvas.width = width
    canvas.height = height
    context.rect(0, 0, width, height)
    context.clearRect(0, 0, width, height)
    context.drawImage(myImage, 0, 0, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height)
    context.font = '14px sans-serif'
    context.textAlign = 'left'
    context.fillStyle = '#fff'
    const left = 80
    const top = 30
    texts.forEach((text: any, index: number) => {
      context.fillText(text, left, top + index * 25)
    })
    if (callback) {
      callback({
        img: canvas.toDataURL(),
        offsetx: 152,
        offsety: -40,
        type: 'red'
      })
      canvas = undefined
    }
  }
}
export const useAttrPanel = defineStore('panel', () => {
  const panels: Ref<string[]> = ref([])

  /**
   * 弹出设备属性面板
   * @param deviceCode 设备编码
   * @param extraAttr 额外属性
   */
  async function addPanel(deviceCode: string, extraAttr?: string[]) {
    clear()
    const id = await addBillboardDiv(deviceCode, extraAttr)
    id && panels.value.push(id)
  }

  function clear() {
    const dataSource = getDataSource('device_attr_panel', App.getInstance().getViewer())
    dataSource.entities.removeAll()
    panels.value = []
  }

  return { addPanel, clear }
})
