<!--
 * @Description: 
 * @Date: 2023-02-13 17:50:01
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-07-06 17:54:57
-->
<template>
  <Draggable
    class="custom-card"
    style="position: fixed"
    :initial-value="{ x: initialValue.x, y: initialValue.y }"
    :prevent-default="true"
    :handle="handle"
  >
    <div class="card">
      <div ref="handle" class="custom-card-top">
        <div class="custom-title">
          <!-- <div class="icon"><img src="@/assets/img/card/title-icon.png" alt="" /></div> -->
          <div>{{ title }}</div>
        </div>
        <el-icon @click="closeHandler" class="custom-icon">
          <close-bold />
        </el-icon>
      </div>
      <div class="custom-card-content">
        <slot></slot>
      </div>
    </div>
  </Draggable>
</template>

<script setup lang="ts">
import { useModuleCard } from '@/stores/ModuleCard'
import { CloseBold } from '@element-plus/icons-vue'
import { UseDraggable as Draggable } from '@vueuse/components'
import { ref } from 'vue'

const props = withDefaults(
  defineProps<{
    title?: string
    closedMethod?: () => void
    uuid: string
    width?: string
    initialValue?: {
      x: number
      y: number
    }
  }>(),
  {
    title: '卡片',
    width: '25%',
    initialValue: () => {
      return {
        x: 1330,
        y: 160
      }
    },
    uuid: '123'
  }
)
const handle = ref<HTMLElement | null>(null)

const closeHandler = () => {
  if (props.closedMethod) {
    props.closedMethod()
  }
  useModuleCard().closeCard(props.uuid)
}
</script>

<style lang="scss" scoped>
.card {
  background: rgba(69, 106, 123, 0.9);
  border: 1px solid #91dcff;
}
.custom-card {
  z-index: 1;
  color: #ffffff;
  width: v-bind(width);
  // border: 1px solid #08c0c3;
  border-radius: 8px 8px 8px 8px;

  .custom-card-top {
    height: 28px;
    box-sizing: border-box;
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(90deg, rgba(70, 178, 255, 0.47) 0%, rgba(51, 204, 204, 0) 100%);
    .custom-title {
      color: #ffffff !important;
      font-size: 16px;
      font-weight: normal;
      letter-spacing: 0.96px;
      line-height: 28px;
      display: flex;
    }
    .custom-icon {
      color: $font-color-info;
      cursor: pointer;
    }
    .icon {
      margin-top: 4px;
      margin-right: 5px;
    }
  }

  .custom-card-content {
    border-radius: 0px 0px 4px 4px;
  }
}
</style>
