import { RoamingManager } from '@/base/roam/RoamingManager'
import { LayerManager } from '@/base/layer/LayerManager'
export class App {
  private static _instance: App = new App()
  private _viewer: BC.Viewer | undefined = undefined
  private _measureUtil: BC.Measure | undefined = undefined
  private _plotUtil: BC.Plot | undefined = undefined
  private _clipUtil: BC.ClippingTileset | undefined = undefined
  private _clipLayer: BC.VectorLayer | undefined = undefined
  private _roamingManager: RoamingManager | undefined = undefined
  private _layerManager: LayerManager | undefined = undefined
  static getInstance(): App {
    if (!this._instance) {
      this._instance = new App()
    }
    return this._instance
  }
  initViewer(viewer: BC.Viewer) {
    this._viewer = viewer
  }

  getViewer() {
    if (!this._viewer) {
      throw new Cesium.DeveloperError('当前Viewer还没有初始化！')
    }
    return this._viewer
  }

  get LayerManager() {
    if (!this._layerManager) {
      this._layerManager = new LayerManager(this._viewer as BC.Viewer)
    }
    return this._layerManager
  }
  getMeasureUtil() {
    if (!this._measureUtil) {
      this._measureUtil = new BC.Measure()
      this.getViewer().use(this._measureUtil)
    }
    return this._measureUtil
  }

  getPlotUtil() {
    if (!this._plotUtil) {
      this._plotUtil = new BC.Plot(this._viewer as BC.Viewer)
    }
    return this._plotUtil
  }

  getClipLayer() {
    if (!this._clipLayer) {
      this._clipLayer = new BC.VectorLayer('clipLayer')
      this.getViewer().addLayer(this._clipLayer)
    }
    return this._clipLayer
  }
  get RoamingManager() {
    if (!this._roamingManager) {
      this._roamingManager = new RoamingManager(this._viewer as BC.Viewer)
    }
    return this._roamingManager
  }

  getClipUtil() {
    if (!this._clipUtil) {
      this._clipUtil = new (BC as any).ClippingTileset()
      ;(this._viewer as BC.Viewer).use(this._clipUtil)
    }
    return this._clipUtil
  }

  reset2DView() {
    if (this._viewer) {
      const { Cesium } = BC.Namespace
      this._viewer.scene.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          106.19811470107489,
          29.656046781995794,
          1384.5800427508075
        ),
        orientation: {
          heading: Cesium.Math.toRadians(0),
          pitch: Cesium.Math.toRadians(-90),
          roll: Cesium.Math.toRadians(0)
        }
      })
    }
  }

  resetView(complete?: any) {
    if (this._viewer) {
      const { Cesium } = BC.Namespace
      this._viewer.scene.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(
          103.70448710079388,
          29.51856735773868,
          2571.4206277133408
        ),
        orientation: {
          heading: Cesium.Math.toRadians(330.21863372023046),
          pitch: Cesium.Math.toRadians(-65.62747176845554),
          roll: Cesium.Math.toRadians(0.0003386482231634175)
        },
        // duration: 5,
        // flyOverLongitude: Cesium.Math.toRadians(60.0),
        // pitchAdjustHeight: 1000, // 如果摄像机飞越高于该值，则调整俯仰俯仰的俯仰角度，并将地球保持在视口中。
        // maximumHeight: 5000, // 相机最大飞行高度
        // flyOverLongitude: 100, // 如果到达目的地有2种方式，设置具体值后会强制选择方向飞过这个经度(这个，很好用）
        // flyOverLongitudeWeight: 100
        // complete: complete,
      })
    }
  }
}
