<!--
 * @Description: 
 * @Date: 2023-02-09 10:02:35
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2024-06-24 11:05:34
-->
<template>
  <el-dropdown popper-class="custom-user-dropdown">
    <div class="el-dropdown-link">
      <el-avatar class="user-avater" :src="getImages('user.png')" />
      <div class="userName">{{ userName }}</div>
    </div>
    <template #dropdown>
      <el-dropdown-menu class="logout">
        <el-dropdown-item @click="exitSystem"> {{ '退出登录' }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import LocalCache from '@/utils/auth'
import { useRouter } from 'vue-router'
import { getImages } from '@/utils/getImages'
const userName = ref('用户')
onMounted(() => {
  userName.value = LocalCache.getCache('realName')
})
const router = useRouter()
const exitSystem = () => {
  // LocalCache.clearCache()
  // router.push('/')
}
</script>

<style scoped lang="scss">
.el-dropdown {
  cursor: pointer;
}
.el-dropdown-link {
  display: flex;
  align-items: center;
  &:focus-visible {
    outline: unset;
  }
  .userName {
    margin-left: 20px;
    margin-top: 4px;
    font-size: 16px;
    font-family:
      Source Han Sans CN,
      Source Han Sans CN-Medium;
    font-weight: 500;
    color: $font-color;
  }
}
:deep(.el-dropdown-menu__item) {
  color: $header-bg;
  &:hover {
    border: unset;
    color: $header-bg;
  }
  &:not(.is-disabled):focus {
    color: $header-bg;
  }
}
</style>
