<template>
  <div class="left-body">
    <BaseTitle title="综合管线" />
    <div class="left-section">
      <base-visual-panel class="pipeline-section">
        <div class="add-row">
          <!-- <div class="cabin-label">
            <el-icon><IconLocation /></el-icon>
            <span>电力舱一区</span>
          </div> -->
          <el-button class="custom-confirm-btn" @click="addPipeLine">
            <el-icon>
              <icon-ep-plus></icon-ep-plus>
            </el-icon>
            新增
          </el-button>
          <el-radio-group class="custom-radio-group" v-model="currTab" @change="changeRadio">
            <el-radio-button value="card">
              <icon-ep-menu class="radio-icon"></icon-ep-menu>
            </el-radio-button>
            <el-radio-button value="list">
              <icon-solar-list-outline class="radio-icon"></icon-solar-list-outline>
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="pipeline-info custom-scroll" v-loading="listLoading">
          <div class="pipeline-item" v-for="item in pipelineList" :key="item.id">
            <div class="pipeline-item-title">
              <span>{{ item.name }}</span>
              <div :class="[item.status === 0 ? 'pipeline-info-normal' : 'pipeline-info-error']">
                <i></i>
                <span>{{ getStatusTxt(item.status) }}</span>
              </div>
            </div>
            <div class="pipeline-item-info">
              <div>
                <span>管线类型: </span>
                <span>{{ getType(item.type) }}</span>
              </div>
              <div>
                <span>入廊长度: </span>
                <span v-if="item.length">{{ item.length }}m</span>
              </div>
              <div>
                <span>联系电话: </span>
                <span>{{ item.telephone }}</span>
              </div>
              <div>
                <span>更新时间: </span>
                <span>{{ item.gmtModified }}</span>
              </div>
            </div>
            <!-- <el-divider class="pipeline-divider"></el-divider> -->
            <el-row class="pipeline-btns">
              <!-- <el-button class="custom-confirm-btn" v-loading="btnLoading">高亮</el-button> -->

              <el-col :span="8">
                <div
                  v-if="testHex(item.material)"
                  class="material-option-img"
                  :style="{ background: item.material }"
                ></div>
                <img
                  v-else-if="!testHex(item.material)"
                  class="material-option-img"
                  :src="getImages(`pipeline/${item.material}`)"
                  alt=""
                />
              </el-col>
              <el-col :span="16"
                ><el-button
                  class="custom-confirm-btn"
                  v-loading="btnLoading"
                  @click="checkHandler(item.id)"
                  >详情</el-button
                ><el-button
                  class="custom-confirm-btn"
                  v-loading="btnLoading"
                  @click="editHandler(item.id)"
                  >编辑</el-button
                ><el-button
                  class="custom-confirm-btn"
                  v-loading="btnLoading"
                  @click="deleteHandler(item)"
                  >删除</el-button
                ></el-col
              >

            </el-row>
          </div>
          <div class="no-data" v-if="!pipelineList.length">暂无数据</div>
        </div>
      </base-visual-panel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { deletePipeline } from '@/api/device_server'
import { App } from '@/base/App'
import { getImages } from '@/utils/getImages'
const cardModule = useModuleCard()

const { mainComponents } = storeToRefs(cardModule)

const pipelineStore = usePipeline()
const { pipelineList, listLoading, pipelineType } = storeToRefs(pipelineStore)
const { getPipelineList, getPipeLineType } = pipelineStore

// 查询管线列表
getPipelineList()
const testHex = (hex: string) => {
  const regex = /^#[0-9A-F]{6}$/i
  return regex.test(hex)
}
onMounted(async () => {
  await getPipeLineType()
})

const currTab = ref('card')

watch(
  () => mainComponents.value,
  (val) => {
    console.log(1)

    const hadList = val.find((item: any) => item.name === 'PipeListCard')
    if (hadList) {
      currTab.value = 'list'
    } else {
      currTab.value = 'card'
    }
  },
  {
    deep: true
  }
)

const changeRadio = (val: string | number | boolean | undefined) => {
  if (val == 'list') {
    cardModule.changeCard({
      name: 'PipeListCard',
      properties: {
        title: '管线列表',
        width: '85%',
        initialValue: { x: 100, y: 80 }
      }
    })
  }
}

const openCard = (obj?: { [key: string]: any }) => {
  cardModule.changeCard({
    name: 'PipeLineCard',
    properties: { title: '管线新增', width: '36%', initialValue: { x: 500, y: 160 }, ...obj }
  })
}

const addPipeLine = () => {
  openCard()
}

const btnLoading = ref(false)

const checkHandler = (id: string) => {
  openCard({ disabled: true, id })
}

const editHandler = (id: string) => {
  openCard({ id })
}

const deleteHandler = (row: any) => {
  useDelMessageBox(() => {
    btnLoading.value = true
    deletePipeline(row.id)
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success(res.message)
          getPipelineList()
          removePipeLines([row])
        }
      })
      .catch((err) => console.error(err, 'delete pipeline error'))
      .finally(() => (btnLoading.value = false))
  })
}
const removePipeLines = (items: any) => {
  const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === '管线')
  if (layers.length > 0) {
    const layer: any = layers[0]
    layer.removePipeLineeByItems(items)
  }
}

const getType = (type: string) => {
  return pipelineType.value.find((item: any) => item.id === type)?.dictLabel
}

const getStatusTxt = (val: number): string => {
  let txt = ''
  switch (val) {
    case 0:
      txt = '正常'
      break
    case 1:
      txt = '停用'
      break
    case 2:
      txt = '故障'
      break
    default:
      break
  }
  return txt
}
</script>

<style scoped lang="scss">
.left-body {
  width: 380px;
  height: 100%;
}
.left-section {
  height: calc(100% - 33px);
  padding-top: 8px;
}
.pipeline-section {
  width: inherit;
  height: 100%;
  padding: 40px 20px;
  border-radius: 6px;
}
.add-row {
  width: inherit;
  height: 34px;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  // border: 1px solid #31bab2;
  border-radius: 4px;
}
.cabin-label {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 16px;
  column-gap: 8px;
  span {
    line-height: 1;
  }
}
.pipeline-info {
  width: inherit;
  height: calc(100% - 58px);
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  row-gap: 24px;
  margin-top: 24px;
  padding-right: 2px;
  overflow-y: scroll;
}
.pipeline-item {
  inline-size: inherit;
  min-block-size: 154px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 12px 8px;
  background: linear-gradient(
    360deg,
    rgba(98, 148, 209, 0.7412) 0%,
    rgba(138, 176, 211, 0.65) 100%
  );
  border: 1px solid #92dcff;
}

@mixin pipeline-info-base($color) {
  width: 50px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 8px;
  border: 1px solid $color;
  color: $color;
  i {
    width: 2px;
    height: 2px;
    border-radius: 2px;
    background-color: $color;
    box-shadow: 0 0 2.5px 1.2px $color;
  }
  span {
    font-size: 10px;
  }
}
.pipeline-item-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // 左侧标题
  > span {
    line-height: 1;
    border-left: 2px solid #78cbff;
    padding-left: 8px;
  }
  .pipeline-info-normal {
    @include pipeline-info-base(#5fc2ff);
  }
  .pipeline-info-error {
    @include pipeline-info-base(#ffa0a0);
  }
}

.pipeline-item-info {
  height: 72px;
  display: flex;
  flex-wrap: wrap;
  align-content: space-evenly;
  padding-bottom: 8px;
  border-bottom: 1px solid #7ac8bb;
  > div:nth-child(odd) {
    width: 46%;
  }
  > div:nth-child(even) {
    width: 54%;
  }
  > div {
    // width: 49%;
    font-size: 12px;
    > span:first-child {
      color: $secondary;
    }
    > span:nth-child(2) {
      margin-left: 4px;
    }
  }
}
.pipeline-divider {
  margin: unset;
  margin-top: 8px;
}
.pipeline-btns {
  // align-self: flex-end;
  .el-button {
    width: 60px;
    height: 22px;
    color: #fff;
  }
}
.radio-icon {
  font-size: 20px;
}
:deep(.el-radio-button__inner) {
  --el-radio-button-checked-bg-color: var(--base-color);
  --el-radio-button-checked-border-color: var(--base-color);
  padding: 4px 8px;
  color: var(--base-color);
  border-color: var(--base-color);
  background: transparent;
}
:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-left-color: var(--base-color);
}
.material-option-img {
  position: absolute;
  // left: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 16px;
}
</style>
