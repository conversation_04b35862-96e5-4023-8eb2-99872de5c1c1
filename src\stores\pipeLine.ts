import { findDictData, queryList } from '@/api/device_server'

export const usePipeline = defineStore('pipeline', () => {
  const listLoading = ref(false)
  const pipelineList = ref<Pipeline.ListItem[]>([])
  const pipelineType = ref()
  const pipelineJson = ref();
  const flyJson = ref();
  const getPipelineList = () => {
    listLoading.value = true
    queryList()
      .then((res) => {
        if (res.code === 200) {
          pipelineList.value = res.data
        }
      })
      .catch((err) => console.error(err, 'get pipeline list error'))
      .finally(() => (listLoading.value = false))
  }

  const getPipeLineType = async () => {
    const { code, data } = await findDictData({
      current: 1,
      query: {
        dictTypeId: '02144a59c53c8a147c3dc19adcf9f68a',
        keyword: ''
      },
      size: 10
    })
    if (code === '00000') {
      pipelineType.value = data.records
    }
  }

  const getPipeLineJson = async () => {
    const result = await fetch('data/partResult1.json')
    const json = await result.json();
    pipelineJson.value = json;
  }
  const getFlyJson = async () => {
    const result = await fetch('data/fly.geojson')
    const json = await result.json();
    flyJson.value = json;
  }

  return {
    listLoading,
    pipelineList,
    getPipelineList,
    getPipeLineType,
    getPipeLineJson,
    getFlyJson,
    flyJson,
    pipelineJson,
    pipelineType
  }
})
