// --------------------- form label两端对齐 ----------------------
.el-form-item__label {
  text-align: justify;
}
.el-form-item__label::after {
  content: ' ';
  width: 100%;
  display: inline-block;
}
.el-form-item__label::before {
  display: none;
}
.slot-form-label {
  position: relative;
}
.required-form-label {
  position: relative;
  padding-left: 8px;
}
.required-form-label::after {
  content: '*';
  width: 100%;
  display: inline-block;
  position: absolute;
  left: 0;
  color: #f56c6c;
}

// ----------------- input style --------------------
.custom__input {
  --el-input-bg-color: transparent;
  --el-input-border-color: var(--base-color);
  --el-input-hover-border-color: var(--base-color);
  --el-input-focus-border-color: var(--base-color);
  --el-input-placeholder-color: var(--fill);
  --el-input-text-color: var(--wt);
  --el-disabled-bg-color: transparent;
  --el-disabled-border-color: var(--base-color);
}
.custom__select {
  --el-select-input-focus-border-color: var(--base-color);
  --el-border-color-hover: var(--base-color);
  --el-color-primary: var(--base-color);
  --el-text-color-placeholder: var(--fill);
}

// --------------- form and children style----------------
.custom-style {
  .el-form-item__label {
    display: inline-block;
    --el-text-color-regular: var(--base-color-light);
  }

  .el-input {
    @extend .custom__input;
  }

  .el-select {
    @extend .custom__select;
    --el-fill-color-blank: transparent;
    --el-border-color: var(--base-color);
    --el-input-text-color: var(--wt);
    .el-select__wrapper.is-disabled {
      background-color: transparent;
      --el-select-disabled-border: var(--base-color);
    }
  }
  .el-select.drawer-select {
    @extend .custom__select;
    --el-fill-color-blank: var(--wt);
    --el-border-color: var(--wt);
    --el-input-text-color: var(--info);
  }

  .el-checkbox {
    --el-checkbox-text-color: var(--base-color-light);
    --el-checkbox-bg-color: transparent;
    --el-checkbox-checked-text-color: var(--base-color-light);
    --el-checkbox-checked-input-border-color: var(--base-color);
    --el-checkbox-checked-bg-color: var(--base-color);
    --el-checkbox-input-border-color-hover: var(--base-color);
    --el-checkbox-disabled-input-fill: transparent;
    --el-checkbox-disabled-checked-input-fill: var(--base-color);
    --el-checkbox-disabled-checked-input-border-color: var(--base-color);
    --el-checkbox-disabled-border-color: var(--base-color);
    --el-checkbox-disabled-checked-icon-color: var(--wt);
  }

  .el-textarea {
    @extend .custom__input;
  }

  .layer-tree {
    width: 100%;
    color: var(--wt);
    font-family: Source-CN-Normal;
    background: transparent;

    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      background-color: transparent;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: var(--base-color);
      background-color: var(--base-color);
    }
    .el-tree-node__content {
      margin: 4px 0;
    }
    .el-tree-node__content:hover {
      background-color: unset;
      color: var(--tree-text-light);
      .el-checkbox__inner {
        border-color: var(--base-color);
      }
      .el-tree-node__expand-icon {
        color: var(--base-color);
      }
    }
    .el-checkbox__inner:after {
      width: 4px;
      height: 9px;
      left: 5px;
      top: 0;
    }
    .el-tree-node__expand-icon {
      color: #fff;
    }

    .el-tree-node:focus > .el-tree-node__content {
      background-color: unset;
    }
    .el-tree-node.is-checked > .el-tree-node__content {
      color: var(--tree-text-light);
    }
  }
  .is-penultimate > .el-tree-node__content {
    color: var(--tree-text-light);
    .el-tree-node__expand-icon {
      color: var(--tree-text-light);
    }
    .el-checkbox__inner {
      border-color: var(--tree-text-light);
    }
  }
}

// --------------- select popper ------------------
.custom-select-popper {
  --el-bg-color-overlay: var(--fill-popper);
  --el-border-color-light: var(--fill-popper);
  --el-text-color-secondary: var(--fill);
  --el-text-color-regular: var(--fill);
  --el-fill-color-light: var(--fill-hover);
  --el-color-primary: var(--wt);
  .el-select-dropdown__item.is-hovering {
    color: var(--wt);
  }
}

// --------------- date-picker popper ---------------
.el-picker__popper.custom-date-picker-popper {
  --el-bg-color-overlay: var(--fill-popper);
  --el-disabled-border-color: var(--base-color);
  --el-text-color-primary: var(--base-color-light);
  --el-text-color-regular: var(--fill);
  --el-border-color-lighter: var(--base-color);
  .el-date-table td.available:hover,
  .el-date-picker__header-label:hover,
  .el-picker-panel__icon-btn:hover {
    color: var(--wt);
  }
  .el-date-table td.today .el-date-table-cell__text {
    color: var(--wt);
  }
  .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: var(--base-color);
  }
}

// -------------dropdown popper --------------
.el-popper.custom-user-dropdown {
  --el-border-color-light: $primary-fill;
}

// --------------- dialog style ----------------
.el-dialog.custom-dialog {
  --el-dialog-bg-color: var(--fill-popper);

  .el-dialog__title {
    --el-text-color-primary: var(--wt);
  }
  .el-dialog__headerbtn .el-dialog__close {
    --el-color-info: var(--base-color-light);
  }
}

// --------------- table style ------------------
.el-table.custom-table {
  --el-table-header-text-color: var(--cell-color);
  --el-table-text-color: var(--wt);
  --el-table-bg-color: rgba(73, 139, 170, 0.42);
  --el-table-header-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-border-color: var(--fill-hover);
  --el-table-row-hover-bg-color: var(--fill-hover);
  .el-button {
    color: var(--wt);
    --el-button-hover-link-text-color: var(--base-color-light);
  }
}
.el-table.custom-pipeline_list {
  --el-table-header-text-color: var(--cell-color);
  --el-table-text-color: var(--wt);
  --el-table-bg-color: rgba(73, 139, 170, 0.42);
  --el-table-header-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-border-color: rgba(127, 199, 244, 0.34);
  --el-table-row-hover-bg-color: var(--fill-hover);
  .el-table__cell {
    padding: 14px 0;
  }
  .el-button {
    color: var(--wt);
    --el-button-hover-link-text-color: var(--base-color-light);
  }
}

// --------------- tree style (no select) ------------------
.el-tree.custom-tree-default {
  --el-fill-color-blank: transparent;
  --el-tree-text-color: var(--wt);
  --el-tree-expand-icon-color: var(--wt);
  --el-tree-node-hover-bg-color: var(--fill-hover);
  --el-color-primary-light-9: var(--fill-hover);
}

// --------------- switch style -------------------
.el-switch.custom-switch {
  --el-switch-on-color: var(--base-color);
  --el-switch-off-color: var(--fill);
}

// -------------- message box style ----------------
.el-message-box.custom-message-box {
  --el-messagebox-title-color: var(--wt);
  --el-messagebox-content-color: var(--wt);
  background-color: var(--fill-popper);
  .el-message-box__headerbtn .el-message-box__close {
    color: var(--base-color-light);
  }
}

// ------------- button cancel-btn/confirm-btn style -----------------
.custom__btn {
  --el-button-text-color: var(--wt);
  --el-button-hover-text-color: var(--wt);
}
.el-button.custom-cancel-btn {
  @extend .custom__btn;
  --el-button-bg-color: var(--cancel-btn-bg);
  --el-button-border-color: var(--cancel-btn-bg);
  --el-button-hover-bg-color: var(--cancel-btn-hover-bg);
  --el-button-hover-border-color: var(--cancel-btn-hover-bg);
  --el-button-active-bg-color: var(--cancel-btn-active-color);
  --el-button-active-border-color: var(--cancel-btn-active-color);
  --el-button-disabled-bg-color: var(--cancel-btn-bg);
  --el-button-disabled-border-color: var(--cancel-btn-bg);
}
.el-button.custom-confirm-btn {
  @extend .custom__btn;
  --el-button-bg-color: var(--base-color);
  --el-button-border-color: var(--base-color);
  --el-button-hover-bg-color: var(--confirm-btn-hover-bg);
  --el-button-hover-border-color: var(--confirm-btn-hover-bg);
  --el-button-active-bg-color: var(--cancel-btn-active-color);
  --el-button-active-border-color: var(--cancel-btn-active-color);
  --el-button-disabled-bg-color: var(--base-color);
  --el-button-disabled-border-color: var(--base-color);
}
.el-button.custom-link-btn {
  --el-button-active-color: var(--base-color-light);
}

// ------------- loading mask style ----------------
.el-loading-mask {
  --el-mask-color: var(--mask-color);
  --el-color-primary: var(--base-color);
}

.el-input-number.custom-input-number {
  --el-fill-color-light: var(--base-color);
  --el-text-color-regular: var(--wt);
  --el-color-primary: var(--wt);
  .el-input__wrapper,
  .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px var(--base-color) inset;
  }
}

// .custom-radio-group {

// }
