<template>
  <div>
    <el-pagination
      :background="background"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :layout="layout"
      class="custom-pagis"
      :page-sizes="pageSizes as any"
      :total="total"
      @size-change="handleSizeChange"
      popper-class="custom-pagi-select custom-select-popper"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
  total: {
    required: true,
    type: Number
  },
  page: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 10
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50]
    }
  },
  layout: {
    type: String,
    default: ' sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  autoScroll: {
    type: Boolean,
    default: true
  },
  hidden: {
    type: Boolean,
    default: false
  },
  bodyStyle: {
    type: Object,
    default: () => {
      return {
        padding: '16px 24px'
      }
    }
  }
})
const emits = defineEmits(['update:page', 'update:limit', 'pagination'])
const currentPage = computed({
  get() {
    return props.page
  },
  set(val) {
    emits('update:page', val)
  }
})
const pageSize = computed({
  get() {
    return props.limit
  },
  set(val) {
    emits('update:limit', val)
  }
})
// 切换条数
const handleSizeChange = (val: any) => {
  pageSize.value = val
  emits('pagination')
}
// 切换页
const handleCurrentChange = (val: any) => {
  currentPage.value = val
  emits('pagination')
}
</script>
