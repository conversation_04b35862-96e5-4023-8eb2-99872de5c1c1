import hRequest from '@/utils/http/object_server'
import type { DataType } from '@/utils/http/types'

export const getDeviceByIds = (data: any) => {
  return hRequest.get<DataType>({
    url: '/open/real/time/data/listDeviceObjectNameInfoVOByDeviceIds',
    params: data
  })
}
export const queryAllDeviceByCode = (data: any) => {
  return hRequest.get<DataType>({
    url: '/open/real/time/data/list/queryAllDeviceByCode',
    params: data
  })
}

//查询分区列表和舱室列表，查分区传值tonghenanlu_fenqu，查舱室传分区code
export const queryChildObjectByCode = (data: any) => {
  return hRequest.get<DataType>({
    url: '/open/real/time/data/list/queryChildObjectByCode',
    params: data
  })
}


//获取所有舱室
export const getAllCarbins = (data?: any) => {
  return hRequest.post<DataType>({
    url: `/admin/obj/generalObject/getAllParentNodeByDeviceIds`,
    data: data
  })
}

