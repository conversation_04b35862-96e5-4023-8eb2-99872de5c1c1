import { getImages } from '@/utils/getImages'

// 管廊全景-管线信息
export const galleryInfo = {
  feedWater: '122293',
  power: '122293',
  water: '122293'
}

// 管廊全景-月度设备维修
export const repairList = [
  {
    sort: 'TOP1',
    label: '安防',
    value: 30
  },
  {
    sort: 'TOP2',
    label: '消防',
    value: 14
  },
  {
    sort: 'TOP3',
    label: '环控',
    value: 13
  },
  {
    sort: 'TOP4',
    label: '通信',
    value: 12
  },
  {
    sort: 'TOP5',
    label: '其他',
    value: 8
  }
]

// 管廊全景-当前隐患数量
export const dangersList = [
  {
    label: '电力舱',
    herf: getImages('overView/danger-1.png'),
    value: 24
  },
  {
    label: '综合舱',
    herf: getImages('overView/danger-2.png'),
    value: 8
  }
]


// 管线管理-运行状态
export const runningState = [
  {
    label: '正常',
    value: 0
  },
  {
    label: '故障',
    value: 2
  },
  {
    label: '停用',
    value: 1
  }
]

// 管线管理-管线材质
export const pipelineMaterials = [
  {
    label: '颜色一',
    value: '#16d79f',
    type: 'hex'
  },
  {
    label: '颜色二',
    value: '#2b579a',
    type: 'hex'
  },
  {
    label: '颜色三',
    value: '#4bb0ee',
    type: 'hex'
  },
  {
    label: '材质一',
    value: 'pipe.png',
    type: 'image',
    src: getImages('pipeline/pipe.png')
  },
  {
    label: '材质二',
    value: 'pipe4.png',
    type: 'image',
    src: getImages('pipeline/pipe4.png')
  },
  {
    label: '材质三',
    value: 'pipe3.png',
    type: 'image',
    src: getImages('pipeline/pipe3.png')
  }
]

// 管线管理-入廊分区
export const cabins = [
  {
    label: '综合舱',
    value: '1Z'
  },
  {
    label: '电力舱',
    value: '1D',
    disabled: true
  }
]
export const cabinAreas = [
  {
    label: '一分区',
    value: '01'
  },
  {
    label: '二分区',
    value: '02'
  },
  {
    label: '三分区',
    value: '03'
  },
  {
    label: '四分区',
    value: '04'
  },
  {
    label: '五分区',
    value: '05'
  },
  {
    label: '六分区',
    value: '06'
  },
  {
    label: '七分区',
    value: '07'
  },
  {
    label: '八分区',
    value: '08'
  },
  {
    label: '九分区',
    value: '09'
  },
  {
    label: '十分区',
    value: '10'
  },
  {
    label: '十一分区',
    value: '11'
  },
  {
    label: '十二分区',
    value: '12'
  },
  {
    label: '十三分区',
    value: '13'
  },
  {
    label: '十四分区',
    value: '14'
  },
  {
    label: '十五分区',
    value: '15'
  }
]

// 管线管理- 入廊位置
export const location = [
  {
    label: '左侧',
    value: 'L',
    disabled: true
  },
  {
    label: '右侧',
    value: 'R'
  }
]
export const locationRows = [
  {
    label: '第一排',
    value: '01'
  },
  {
    label: '第二排',
    value: '02'
  },
  {
    label: '第三排',
    value: '03'
  },
  {
    label: '第四排',
    value: '04'
  },
  {
    label: '第五排',
    value: '05'
  },
  {
    label: '第六排',
    value: '06'
  }
]
export const locationCols = [
  {
    label: '第一根',
    value: '01'
  },
  {
    label: '第二根',
    value: '02'
  },
  {
    label: '第三根',
    value: '03'
  },
  {
    label: '第四根',
    value: '04'
  }
]
