import { ImageryLayer } from "./ImageryLayer";
import { LayerGroup } from "./LayerGroup";
import { PipeLayer } from "./PipeLayer";
import { TerrainLayer } from "./TerrainLayer";
import { CustomHtmlLayer } from "./CustomHtmlLayer";
import { TilesetLayer } from "./TilesetLayer";
import { ModelLayer } from "./ModelLayer";


export class LayerFactory {
  static crateLayer(type: string, options: any) {
    switch (type) {
      case "imagery":
        return new ImageryLayer(options);
      case "terrain":
        return new TerrainLayer(options);
      case "tileset":
        return new TilesetLayer(options);
      case "pipeline":
        return new PipeLayer(options);
      case "model":
        return new ModelLayer(options);
      case "divIcon":
        return new CustomHtmlLayer(options);
      case "group":
      default:
        return new LayerGroup(options);
    }
  }
}
