import { getPrimitiveCollection } from "@/base/utils/CesiumUtils";
import { BaseLayer } from "./BaseLayer";


export class ModelLayer extends BaseLayer {
  protected declare _delegate: any;
  async addToInternal(viewer: BC.Viewer) {
    const response = await fetch('data/treePosition.json');
    const data = await response.json();
    this._delegate = getPrimitiveCollection('treeModel', viewer);
    data.forEach((item: any) => {
      this.addModel(item)
    })
    return this._delegate;
  }

  addModel(item: any) {
    const modelFile = 'model/tree.glb'
    const { Cesium } = BC.Namespace
    // let longitude = 103.70179569274995
    // let latitude = 29.530811092873613
    // let altitude = 16.596541845896162
    const position = Cesium.Cartesian3.fromDegrees(item[0], item[1], item[2] - 1)
    // const cartographic = Cesium.Cartographic.fromCartesian(position)
    const { model, modelMatrix } = this.loadGltfModel(modelFile, position)
    // model.readyPromise.then((model: any) => {
    //   model.activeAnimations.addAll({
    //     speedup: 2.0,
    //     loop: Cesium.ModelAnimationLoop.REPEAT,
    //     reverse: true
    //   })
    // })
  }
  
  loadGltfModel(modelFile: string, position: any) {
    const { Cesium } = BC.Namespace
    const modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position)
    const model = this._delegate.add(
      Cesium.Model.fromGltf({
        url: modelFile,
        show: true,
        modelMatrix: modelMatrix,
        scale: 1.0,
        maximumScale: 1.0,
        minimumPixelSize: 128
        // maximumScale: 20000,
        // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      })
    )
    return {
      model: model,
      modelMatrix: modelMatrix
    }
  }
  showInternal(show: boolean) {
    this._delegate.show = show;
  }


  removeInternal() {
      this._viewer.scene.primitives.remove(this._delegate);
  }
}
