<!--
 * @Description: 
 * @Date: 2024-03-25 10:03:42
 * @Author: GISerZ
 * @LastEditors: xiao
 * @LastEditTime: 2024-06-27 15:10:09
-->
<template>
  <div>
    <MapContainer v-show="!showOpenlayerMap"></MapContainer>
    <OpenlayerMapContainer v-if="showOpenlayerMap"></OpenlayerMapContainer>
    <div v-if="!isTool" class="header-card">
      <div style="width: 100%; height: 100%; display: flex; align-items: center">
        <div class="top-title" @click="backRoot">
          <img :src="getImages('logo.png')" alt="" />
          <div class="system-title">
            <div>综合管廊统一管理平台</div>
            <div>GIS/BIM管理平台</div>
          </div>
        </div>
        <TopMenu></TopMenu>
        <!-- <div class="user">
          <UserToolbar></UserToolbar>
        </div> -->
      </div>
    </div>
    <DrawerLeft v-if="!isTool"></DrawerLeft>
    <AllCards v-if="!isTool"></AllCards>
    <!-- <DeviceDetial v-if="useDevice().show"></DeviceDetial> -->
    <MapFooter v-if="showFooter && !isTool" :type="footerType" ref="mapFooterRef"></MapFooter>
  </div>
</template>

<script setup lang="ts">
import TopMenu from '@/components/TopMenu.vue'
// import UserToolbar from '@/components/UserToolbar.vue'
import DrawerLeft from '@/components/drawer/DrawerLeft.vue'
import MapContainer from '@/components/MapContainer.vue'
import OpenlayerMapContainer from '@/components/OpenlayerMapContainer.vue'
import MapFooter from '@/components/MapFooter.vue'
import AllCards from '@/components/card/AllCards.vue'
import { getImages } from '@/utils/getImages'
import { ModuleDrawer } from '@/components/drawer'
const showOpenlayerMap = ref(false)
const showFooterList = ['LeftPipeline', 'LeftPartitionMonitoring']
const showFooter = ref(false)
const mapFooterRef = ref()
import { useDevice } from '@/stores/device'
const footerType = ref('')
const { isTool } = useTool()
watch(
  () => useDevice().isReload,
  () => {
    useDevice().show = false
    nextTick(() => {
      useDevice().show = true
    })
  }
)

const backRoot = () => {
  window.history.go(-1)
}

onMounted(() => {
  ModuleDrawer.changeLeftDrawer.subscribe((cptName) => {
    if (showFooterList.includes(cptName)) {
      showFooter.value = true
      nextTick(() => {
        mapFooterRef.value.resetData()
      })
    } else {
      showFooter.value = false
    }
    if (cptName === 'LeftPartitionMonitoring') {
      showOpenlayerMap.value = true
      footerType.value = 'partitionMonitoring'
    } else {
      showOpenlayerMap.value = false
      footerType.value = 'pipeline'
    }
  })
})
</script>

<style scoped lang="scss">
.header-card {
  position: absolute;
  left: 0px;
  top: 0;
  width: 100%;
  height: 60px;
  opacity: 1;
  background: $header-bg;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
  z-index: 10;
  .top-title {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 20px;
    flex-basis: 315px;
    cursor: pointer;
  }
  .top-title > img {
    width: 40px;
    height: 40px;
    margin-right: 20px;
  }
  .system-title {
    color: #fff;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }
  .system-title > div:first-child {
    font-size: 20px;
  }
  .system-title > div:nth-child(2) {
    font-size: 14px;
    margin-top: 4px;
  }
}

.user {
  box-sizing: border-box;
  padding-right: 8px;
  margin-inline-start: auto;
}
</style>
