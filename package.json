{"name": "gallery-web3d", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode dev", "prod": "vite --mode prod", "build": "run-p type-check build-only", "build:prod": "vite build --mode prod", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@turf/turf": "^6.5.0", "axios": "^1.4.0", "bcgis-sdk": "1.1.44", "element-plus": "^2.4.4", "pinia": "^2.1.7", "rxjs": "^7.8.1", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@iconify-json/ep": "^1.1.13", "@iconify-json/solar": "^1.1.9", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.19.3", "@vitejs/plugin-vue": "^4.4.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "@vueuse/components": "^10.2.1", "@vueuse/core": "^10.2.1", "deck.gl": "^8.9.34", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "sass": "^1.69.5", "typescript": "~5.3.0", "unplugin-auto-import": "^0.16.4", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vite-plugin-compression": "^0.5.1", "vite-plugin-static-copy": "^1.0.0", "vue-tsc": "^1.8.25"}}