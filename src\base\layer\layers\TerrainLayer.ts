/*
 * @Description: 地形图层
 * @Autor: silei
 * @Date: 2023-08-03 10:24:51
 * @LastEditors: silei
 * @LastEditTime: 2023-08-04 10:14:12
 */

import { BaseLayer } from "./BaseLayer";
const terrainCache: TerrainLayer[] = [];
export class TerrainLayer extends BaseLayer {
  addToInternal(viewer: BC.Viewer) {
    // let data: any;
    // const terrainDetail = JSON.parse(data.url);
    // // const terrain = BC.TerrainFactory.createTerrain(
    // //   terrainDetail.type, {
    // //     url: terrainDetail.url,
    // //   }
    // // );
    // const terrain = BC.TerrainFactory.createTerrain(terrainDetail.type, {
    //   url: terrainDetail.url,
    // });

    const terrain = BC.TerrainFactory.createTerrain(
      this._options.type,
      this._options
    );
    viewer.addTerrain(terrain);
    // viewer.delegate.terrainProvider = terrain;
    terrainCache.push(this);
    return terrain;
  }
  flyTo(): void {
      return;
  }
  showInternal(show: boolean) {
    // 只控制最后一个地形的显示隐藏
    const lastIndex = this.getLastIndex();
    const index = terrainCache.findIndex((t) => t === this);
    if (index >= lastIndex) {
      if (show) {
        this._viewer.delegate.terrainProvider = this._delegate;
      } else {
        // 如果地形存在多个则切换到上一个地形 否则使用默认地形
        const lastTerrainIndex = this.getLastIndex();
        if (lastTerrainIndex > -1) {
          this._viewer.delegate.terrainProvider =
            terrainCache[lastTerrainIndex]._delegate;
        } else {
          this._viewer.delegate.terrainProvider =
            BC.TerrainFactory.createEllipsoidTerrain({});
        }
      }
    }
  }
  protected getLastIndex() {
    for (let i = terrainCache.length - 1; i >= 0; i--) {
      if (terrainCache[i]._show) {
        return i;
      }
    }
    return -1;
  }
  removeInternal() {
    const index = terrainCache.findIndex((t) => t === this);
    terrainCache.splice(index, 1);
    const lastIndex = this.getLastIndex();
    if (lastIndex > -1) {
      this._viewer.delegate.terrainProvider =
        terrainCache[lastIndex]._delegate;
    } else {
      this._viewer.delegate.terrainProvider =
        BC.TerrainFactory.createEllipsoidTerrain({});
    }
  }
}
