<template>
  <BaseCard :title="'基础测量'" :closed-method="close">
    <div class="operate-btns">
      <el-button class="operate-btn custom-confirm-btn" @click="measure('distance')"
        >距离</el-button
      >
      <el-button class="operate-btn custom-confirm-btn" @click="measure('area_surface')"
        >面积</el-button
      >
      <el-button class="operate-btn custom-confirm-btn" @click="measure('height')">高度</el-button>
      <el-button class="operate-btn custom-cancel-btn" @click="deactivate">清除</el-button>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { App } from '@/base/App'
import BaseCard from '@/components/card/BaseCard.vue'
let _measureUtil: any
onUnmounted(() => {
  close()
})
const measure = (type: string) => {
  console.log(type)
  _measureUtil = App.getInstance().getMeasureUtil()
  _measureUtil.activate(type, {
    clampToModel: true,
    lerpNum: 5,
    onDrawFinish: () => {}
  })
}
const deactivate = () => {
  _measureUtil && _measureUtil.deactivate()
}
const close = () => {
  deactivate()
}
</script>

<style lang="scss" scoped></style>
