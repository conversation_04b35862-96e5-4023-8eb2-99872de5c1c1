<template>
  <div class="left-body">
    <BaseTitle title="飞行漫游" />
    <div class="left-section">
      <base-visual-panel class="roaming-section">
        <el-row :gutter="16" type="flex" justify="space-between" class="select-section">
          <el-col :span="12">
            <el-select
              @change="selectPartition"
              class="drawer-select"
              v-model="currentPartiton"
              placeholder="请选择分区"
            >
              <el-option
                v-for="item in partitionOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
              class="select_partition drawer-select"
              @change="selectCabin"
              v-model="currentCabin"
              placeholder="请选择舱室"
            >
              <el-option
                v-for="item in cabinOptions"
                :key="item.code"
                :label="item.typeDetailName"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-button @click="startRoaming" class="roaming-btn custom-confirm-btn">
          <el-icon><IconRoaming /></el-icon>
          开始漫游
        </el-button>
        <el-button @click="changePause" style="margin-left: 0;" class="roaming-btn custom-confirm-btn">
          <el-icon><IconRoaming /></el-icon>
          {{ pauseBool ? '继续漫游' : '暂停漫游'  }}
        </el-button>
        <el-button
          @click="stopRoaming"
          style="margin-left: 0"
          class="roaming-btn custom-confirm-btn"
        >
          <el-icon><IconRoaming /></el-icon>
          停止漫游
        </el-button>
        <DeviceLocationTitle style="margin-top: 20px" title="漫游进度" />
        <el-slider
          @input="(val) => changeProgress(val)"
          :min="0"
          :max="100"
          :step="1"
          v-model="progress"
        />
        <DeviceLocationTitle style="margin-top: 10px" title="漫游速度" />
        <el-slider @input="(val) => changeSpeed(val)" :min="1" :max="8" :step="1" v-model="speed" />
        <DeviceLocationTitle style="margin-top: 10px; margin-bottom: 10px" title="手动模式" />
        <el-switch @change="changeManualMode" v-model="manualMode" active-text="开启" inactive-text="关闭" />
        <span style="margin-left: 30px">W：前进</span>
        <span style="margin-left: 30px">S：后退</span>
      </base-visual-panel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { queryChildObjectByCode } from '@/api/object_server'
import { App } from '@/base/App';
import DeviceLocationTitle from '@/components/deviceLocation/DeviceLocationTitle.vue'
import _ from 'lodash'
const partitionOptions = ref<any[]>([])
const currentPartiton = ref()
const currentCabin = ref()
const cabinOptions = ref<any[]>([])
onUnmounted(() => {
  window.roamTool.stopRoaming()
})
onMounted(async () => {
  // const viewer = App.getInstance().getViewer()
  // viewer.clock.onTick.addEventListener(_onTick, this);
  await getPartitions()
  await getCabins()
  
})
const _flags: any = reactive({
  moveForward: false,
  moveBackward: false
})
watch(_flags, (newVal, oldVal) => {
  _onTick()
}, { deep: true })
const startRoaming = () => {
  currentCabin.value = 'tonghenanlu_fenqu2_zhc'
  window.roamTool.startRoaming(currentCabin.value, (progressVal: number) => {
    progress.value = Math.floor(progressVal * 100)
  })
}
const pauseBool = ref(false)
const progressFloat = ref(0)
let progressInterval = 0
const manualMode = ref(false)
const changeManualMode = () => {
  if (manualMode.value) {
    addKeyListener()
  } else {
    removeKeyListener()
  }
}
const changePause = () => {
  pauseBool.value = !pauseBool.value
  if (pauseBool.value) {
    window.roamTool.pauseRoaming()
    progressInterval = progressFloat.value
  } else {
    // 继续漫游并传递进度回调函数
    window.roamTool.continueRoaming(currentCabin.value, (progressVal: number) => {
      progress.value = Math.floor(progressVal * 100)
      progressFloat.value = progressVal * 100
    })
  }
}
const stopRoaming = () => {
  window.roamTool.stopRoaming()
}
const getPartitions = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: 'tonghenanlu_fenqu'
  })
  if (code === '00000') {
    partitionOptions.value = data
    currentPartiton.value = data[0].code
  }
}
const getCabins = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: currentPartiton.value
  })
  if (code === '00000') {
    cabinOptions.value = data.filter((item: any) => item.code.indexOf('fenqu') !== -1)
    currentCabin.value = cabinOptions.value[0].code
  }
}
/**
 * 选择舱室
 */
const selectCabin = () => {}
/**
 * 选择分区
 */
const selectPartition = async () => {
  await getCabins()
}
const progress = ref(20)
const changeProgress = (value: any) => {
  window.roamTool.changeProgress(currentCabin.value, value)
}
const speed = ref(3)
const changeSpeed = (value: any) => {
  window.roamTool.changeSpeed(currentCabin.value, value)
}

const addKeyListener = () => {
  document.addEventListener('keydown', _onKeydown, false)
  document.addEventListener('keyup', _onKeyup, false)
  
}
const removeKeyListener = () => {
  document.removeEventListener('keydown', _onKeydown, false)
  document.removeEventListener('keyup', _onKeyup, false)
  // const viewer = App.getInstance().getViewer()
  // viewer.clock.onTick.removeEventListener(_onTick, this);
}

const _onKeydown = (e: any) => {
  let flag = _getFlagForKeyCode(e)
  if (flag) {
    _flags[flag] = true
  }
}
const _onKeyup = (e: any) => {
  Object.keys(_flags).forEach((key) => {
    _flags[key] = false
  })
}
const _onTick = () => {
  console.log(_flags)
  manualMode.value && _flags.moveForward && window.roamTool.changeProgress(currentCabin.value, progress.value + 1);
  manualMode.value && _flags.moveBackward && window.roamTool.changeProgress(currentCabin.value, progress.value - 1);
}

const _getFlagForKeyCode = (e: any) => {
  let flag = undefined
  switch (e.keyCode) {
    case 'W'.charCodeAt(0):
    case 38:
      if (e.shiftKey) {
        flag = 'moveUp'
      } else {
        flag = 'moveForward'
      }
      break
    case 'S'.charCodeAt(0):
    case 40:
      if (e.shiftKey) {
        flag = 'moveDown'
      } else {
        flag = 'moveBackward'
      }
      break
    default:
      break
  }
  return flag
}
</script>

<style scoped lang="scss">
.left-body {
  width: 380px;
  height: 100%;
}
.left-section {
  height: calc(100% - 33px);
  padding-top: 8px;
}
.roaming-section {
  width: inherit;
  height: 100%;
  padding: 20px;
  border-radius: 6px;
}
.roaming-btn {
  width: 100%;
  margin-top: 24px;
  .el-icon {
    margin-right: 4px;
  }
}
</style>
