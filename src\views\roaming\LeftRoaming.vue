<template>
  <div class="left-body">
    <BaseTitle title="飞行漫游" />
    <div class="left-section">
      <base-visual-panel class="roaming-section">
        <el-row :gutter="16" type="flex" justify="space-between" class="select-section">
          <el-col :span="12">
            <el-select
              @change="selectPartition"
              class="drawer-select"
              v-model="currentPartiton"
              placeholder="请选择分区"
            >
              <el-option
                v-for="item in partitionOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
              class="select_partition drawer-select"
              @change="selectCabin"
              v-model="currentCabin"
              placeholder="请选择舱室"
            >
              <el-option
                v-for="item in cabinOptions"
                :key="item.code"
                :label="item.typeDetailName"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-button
          @click="startRoaming"
          :disabled="isRoamingActive || isRoamingPaused"
          :loading="isStarting"
          class="roaming-btn custom-confirm-btn"
        >
          <el-icon><IconRoaming /></el-icon>
          开始漫游
        </el-button>
        <el-button
          @click="changePause"
          :disabled="!isRoamingActive && !isRoamingPaused"
          style="margin-left: 0;"
          class="roaming-btn custom-confirm-btn"
        >
          <el-icon><IconRoaming /></el-icon>
          {{ isRoamingPaused ? '继续漫游' : '暂停漫游'  }}
        </el-button>
        <el-button
          @click="stopRoaming"
          :disabled="!isRoamingActive && !isRoamingPaused"
          style="margin-left: 0"
          class="roaming-btn custom-confirm-btn"
        >
          <el-icon><IconRoaming /></el-icon>
          停止漫游
        </el-button>
        <DeviceLocationTitle style="margin-top: 20px" title="漫游进度" />
        <div style="margin-bottom: 10px; color: #909399; font-size: 12px;">
          状态：{{ roamingStatusText }} | 进度：{{ progress }}%
        </div>
        <el-slider
          @input="(val) => changeProgress(val)"
          :min="0"
          :max="100"
          :step="1"
          :disabled="!isRoamingActive && !isRoamingPaused"
          v-model="progress"
        />
        <DeviceLocationTitle style="margin-top: 10px" title="漫游速度" />
        <el-slider @input="(val) => changeSpeed(val)" :min="1" :max="8" :step="1" v-model="speed" />
        <DeviceLocationTitle style="margin-top: 10px; margin-bottom: 10px" title="手动模式" />
        <el-switch @change="changeManualMode" v-model="manualMode" active-text="开启" inactive-text="关闭" />
        <span style="margin-left: 30px">W：前进</span>
        <span style="margin-left: 30px">S：后退</span>
      </base-visual-panel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { queryChildObjectByCode } from '@/api/object_server'
import DeviceLocationTitle from '@/components/deviceLocation/DeviceLocationTitle.vue'
import { ElMessage } from 'element-plus'

// 基础数据
const partitionOptions = ref<any[]>([])
const currentPartiton = ref()
const currentCabin = ref()
const cabinOptions = ref<any[]>([])

// 漫游状态管理
const isRoamingActive = ref(false)
const isRoamingPaused = ref(false)
const isStarting = ref(false)
const progress = ref(0)
const progressFloat = ref(0)
const speed = ref(3)

// 手动模式
const manualMode = ref(false)

// 计算属性：漫游状态文本
const roamingStatusText = computed(() => {
  if (isRoamingActive.value) return '漫游中'
  if (isRoamingPaused.value) return '已暂停'
  return '未开始'
})
onUnmounted(() => {
  // 清理键盘监听器
  if (manualMode.value) {
    removeKeyListener()
  }

  // 停止漫游并清理状态
  if (isRoamingActive.value || isRoamingPaused.value) {
    window.roamTool.stopRoaming()
  }

  // 重置状态
  isRoamingActive.value = false
  isRoamingPaused.value = false
  progress.value = 0
  progressFloat.value = 0
})
onMounted(async () => {
  // const viewer = App.getInstance().getViewer()
  // viewer.clock.onTick.addEventListener(_onTick, this);
  await getPartitions()
  await getCabins()
  
})
const _flags: any = reactive({
  moveForward: false,
  moveBackward: false
})
watch(_flags, () => {
  _onTick()
}, { deep: true })
/**
 * 开始漫游
 */
const startRoaming = async () => {
  if (isRoamingActive.value || isRoamingPaused.value) {
    ElMessage.warning('漫游已在进行中')
    return
  }

  try {
    isStarting.value = true
    currentCabin.value = 'tonghenanlu_fenqu2_zhc'

    window.roamTool.startRoaming(currentCabin.value, (progressVal: number) => {
      const progressPercent = Math.floor(progressVal * 100);
      progress.value = progressPercent;
      progressFloat.value = progressPercent;
    })

    // 更新状态
    isRoamingActive.value = true
    isRoamingPaused.value = false
    ElMessage.success('漫游已开始')
  } catch (error) {
    console.error('开始漫游失败:', error)
    ElMessage.error('开始漫游失败')
  } finally {
    isStarting.value = false
  }
}
/**
 * 切换手动模式
 */
const changeManualMode = () => {
  if (manualMode.value) {
    addKeyListener()
    ElMessage.info('手动模式已开启，使用 W/S 键控制进度')
  } else {
    removeKeyListener()
    ElMessage.info('手动模式已关闭')
  }
}
/**
 * 暂停/继续漫游
 */
const changePause = () => {
  try {
    if (isRoamingActive.value) {
      // 当前是活动状态，执行暂停
      if (window.roamTool.canPause()) {
        window.roamTool.pauseRoaming()
        isRoamingActive.value = false
        isRoamingPaused.value = true
        ElMessage.success('漫游已暂停')
      } else {
        ElMessage.warning('当前状态无法暂停')
      }
    } else if (isRoamingPaused.value) {
      // 当前是暂停状态，执行继续
      if (window.roamTool.canResume()) {
        window.roamTool.continueRoaming(currentCabin.value, (progressVal: number) => {
          const progressPercent = Math.floor(progressVal * 100);
          progress.value = progressPercent;
          progressFloat.value = progressPercent;
        })
        isRoamingActive.value = true
        isRoamingPaused.value = false
        ElMessage.success('漫游已继续')
      } else {
        ElMessage.warning('当前状态无法继续')
      }
    }
  } catch (error) {
    console.error('暂停/继续漫游失败:', error)
    ElMessage.error('操作失败')
  }
}
/**
 * 停止漫游
 */
const stopRoaming = () => {
  try {
    window.roamTool.stopRoaming()

    // 重置所有状态
    isRoamingActive.value = false
    isRoamingPaused.value = false
    progress.value = 0
    progressFloat.value = 0

    ElMessage.success('漫游已停止')
  } catch (error) {
    console.error('停止漫游失败:', error)
    ElMessage.error('停止漫游失败')
  }
}
const getPartitions = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: 'tonghenanlu_fenqu'
  })
  if (code === '00000') {
    partitionOptions.value = data
    currentPartiton.value = data[0].code
  }
}
const getCabins = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: currentPartiton.value
  })
  if (code === '00000') {
    cabinOptions.value = data.filter((item: any) => item.code.indexOf('fenqu') !== -1)
    currentCabin.value = cabinOptions.value[0].code
  }
}
/**
 * 选择舱室
 */
const selectCabin = () => {}
/**
 * 选择分区
 */
/**
 * 选择分区
 */
const selectPartition = async () => {
  await getCabins()
}

/**
 * 手动调整进度
 */
const changeProgress = (value: any) => {
  // 将UI进度值(0-100)转换为底层进度值(0-1)
  progressFloat.value = value;
  window.roamTool.changeProgress(currentCabin.value, value / 100)
}

/**
 * 调整漫游速度
 */
const changeSpeed = (value: any) => {
  window.roamTool.changeSpeed(currentCabin.value, value)
}

const addKeyListener = () => {
  document.addEventListener('keydown', _onKeydown, false)
  document.addEventListener('keyup', _onKeyup, false)
  
}
const removeKeyListener = () => {
  document.removeEventListener('keydown', _onKeydown, false)
  document.removeEventListener('keyup', _onKeyup, false)
  // const viewer = App.getInstance().getViewer()
  // viewer.clock.onTick.removeEventListener(_onTick, this);
}

const _onKeydown = (e: any) => {
  let flag = _getFlagForKeyCode(e)
  if (flag) {
    _flags[flag] = true
  }
}
const _onKeyup = () => {
  Object.keys(_flags).forEach((key) => {
    _flags[key] = false
  })
}
/**
 * 手动漫游控制
 */
const _onTick = () => {
  // 只在手动模式且漫游激活时响应键盘操作
  if (!manualMode.value || (!isRoamingActive.value && !isRoamingPaused.value)) {
    return;
  }

  if (_flags.moveForward) {
    const newProgress = Math.min(100, progress.value + 1);
    progress.value = newProgress;
    progressFloat.value = newProgress;
    window.roamTool.changeProgress(currentCabin.value, newProgress / 100);
  }

  if (_flags.moveBackward) {
    const newProgress = Math.max(0, progress.value - 1);
    progress.value = newProgress;
    progressFloat.value = newProgress;
    window.roamTool.changeProgress(currentCabin.value, newProgress / 100);
  }
}

const _getFlagForKeyCode = (e: any) => {
  let flag = undefined
  switch (e.keyCode) {
    case 'W'.charCodeAt(0):
    case 38:
      if (e.shiftKey) {
        flag = 'moveUp'
      } else {
        flag = 'moveForward'
      }
      break
    case 'S'.charCodeAt(0):
    case 40:
      if (e.shiftKey) {
        flag = 'moveDown'
      } else {
        flag = 'moveBackward'
      }
      break
    default:
      break
  }
  return flag
}
</script>

<style scoped lang="scss">
.left-body {
  width: 380px;
  height: 100%;
}
.left-section {
  height: calc(100% - 33px);
  padding-top: 8px;
}
.roaming-section {
  width: inherit;
  height: 100%;
  padding: 20px;
  border-radius: 6px;
}
.roaming-btn {
  width: 100%;
  margin-top: 24px;
  .el-icon {
    margin-right: 4px;
  }
}
</style>
