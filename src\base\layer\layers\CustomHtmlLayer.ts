import { getAllPartStatus } from '@/api/device_server'
import { BaseLayer } from './BaseLayer'
export class CustomHtmlLayer extends BaseLayer {
  interval: any
  constructor(options: any) {
    super(options)
  }
  refreshContent() {}

  async addToInternal(viewer: BC.Viewer) {
    let delegate: any
    const result = await fetch(this._options.url)
    const json = await result.json()
    const layer = new BC.HtmlLayer(this.id)
    viewer.addLayer(layer)
    if (this._title === '分区') {
      const { code, data } = await getAllPartStatus()
      if (code === '00000') {
        json.features.forEach((item: any) => {
          const status = data.find((d: any) => d.code === item.properties.code).status
          item.properties.status = Number(status)
        })
      }
    }
    json.features.forEach((item: any) => {
      const position = new BC.Position(
        item.geometry.coordinates[0],
        item.geometry.coordinates[1],
        item.properties.z ? item.properties.z : 5
      )
      let divIcon: any
      if (item.properties.type === 'divBorderLabel') {
        divIcon = new BC.DivIcon(
          position,
          `<div class="mars3d-divBoderLabel">
            <div class="mars3d-divBoderLabel-boder">
              <span class="mars3d-divBoderLabel-text">${item.properties.name}</span>
            </div>
          </div>`
        )
        divIcon.setStyle({
          offset: {
            x: -65,
            y: -20
          }
        })
      } else {
        const imgUrl =
          item.properties.status === 1 ? 'img/map-title-r.png' : 'img/map-title-r-1.png'
        const isAnimate = item.properties.status === 1 ? 'mars3d-animation' : ''
        divIcon = new BC.DivIcon(
          position,
          `<div class="mars-four-color ${isAnimate}">
          <img src="${imgUrl}"  class="four-color_bg"></img>
          <div class="four-color_name" style="color: white">${item.properties.name}</div>
        </div>`
        )
        divIcon.name = item.properties.name
        divIcon.code = item.properties.code
        divIcon.setStyle({
          offset: {
            x: item.properties.status === 1 ? 40 : 45,
            y: 5
          },
          className: 'pointerevents',
          // scaleByDistance: {
          //   near: 0, //最近距离
          //   nearValue: 0, //最近距离值
          //   far: 1, //最远距离值
          //   farValue: 0 //最远距离值
          // }, //根据距离设置比例
          distanceDisplayCondition: {
            near: 0, //最近距离
            far: 4000 //最远距离
          } //根据距离设置可见
        })
      }

      layer.addOverlay(divIcon)
      // layer.show = false
    })
    this._delegate = delegate = layer
    if (this._title === '分区') {
      this.interval = setInterval(async () => {
        const { code, data } = await getAllPartStatus()
        if (code === '00000') {
          layer.eachOverlay((overlay: any) => {
            const status = data.find((d: any) => d.code === overlay.code).status
            const statusNum = Number(status)
            const imgUrl = statusNum === 1 ? 'img/map-title-r.png' : 'img/map-title-r-1.png'
            const isAnimate = statusNum === 1 ? 'mars3d-animation' : ''
            overlay.content = `<div class="mars-four-color ${isAnimate}">
          <img src="${imgUrl}"  class="four-color_bg"></img>
          <div class="four-color_name" style="color: white">${overlay.name}</div>
        </div>`
          }, this)
        }
      }, 1000 * 60 * 10)
    }
    return delegate
  }

  removeInternal() {
    // this._viewer.imageryLayers.remove(this._delegate);
    this._viewer.removeLayer(this._delegate)
    
    this.interval && clearInterval(this.interval)
  }
}
