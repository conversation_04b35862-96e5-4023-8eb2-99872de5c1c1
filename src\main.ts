/*
 * @Description:
 * @Date: 2024-03-25 10:03:42
 * @Author: GISerZ
 * @LastEditors: xiao
 * @LastEditTime: 2024-06-24 11:08:27
 */

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import 'bcgis-sdk/dist/bcgis.core.min.css'
import '@/assets/css/index.scss'
import { Login } from './api/api_server'
import { useTool } from './stores/IsTool'
declare global {
  interface Window {
    CESIUM_BASE_URL: string
    roamTool: any
  }
}
window.CESIUM_BASE_URL = 'libs/bcgis-sdk/resources'
const doLogin = async () => {
  if (import.meta.env.MODE == 'dev') {
    /**
     * 本地运行使用登录的方式获取token
     * 部署从cookie获取, (http目录axios对应文件中)
     */
    // const { code, data } = await <PERSON><PERSON>({
    //   password: 'aaa123!',
    //   username: 'system_admin'
    // })
    // document.cookie = 'Access-Token=' + data.accessToken
    // window.localStorage.setItem('Access-Token', data.accessToken)
    // return true
  } else {
    return true
  }
}

const app = createApp(App)
app.use(createPinia())
app.use(router)
// useTool().change(true)
app.mount('#app')
// doLogin().then(() => {
//   app.mount('#app')
// })
