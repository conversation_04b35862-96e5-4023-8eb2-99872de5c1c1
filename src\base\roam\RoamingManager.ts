/*
 * @Description: 漫游管理器 - BC.RoamingController的封装
 * @Autor: silei
 * @Date: 2023-08-09 11:50:54
 * @LastEditors: silei
 * @LastEditTime: 2023-08-16 14:24:24
 */

import { RoamingPath } from "./type/RoamingPath";
import type { ProgressCallback } from "./type/RoamingTypes";

/**
 * 漫游管理器
 * @description 封装BC.RoamingController，提供统一的漫游控制接口
 */
export class RoamingManager {
  private _rc: BC.RoamingController;

  constructor(protected viewer: BC.Viewer) {
    this._rc = new BC.RoamingController(viewer);
  }



  /**
   * 添加漫游路径
   * @param path
   */
  addPath(path: RoamingPath) {
    const roamPath: any = new BC.RoamingPath(path.points ?? "", {
      heading: path.heading,
      pitch: path.pitch,
      range: path.range,
      speed: path.speed,
    });
    roamPath.id = path.id;
    roamPath.infos = path;
    this._rc.addPath(roamPath);
  }
  /**
   * 激活漫游路径
   * @param id 路径ID
   * @param onProgress 进度回调函数
   */
  activate(id: string, onProgress?: ProgressCallback): boolean {
    try {
      this.deactivate();
      const path: any = this._rc.getPath(id);
      if (!path) {
        console.warn(`漫游路径 ${id} 不存在`);
        return false;
      }

      // 确保速度设置正确
      if (path.infos && path.infos.speed) {
        path.speed = path.infos.speed;
      }

      this._rc.activate(path, onProgress);
      return true;
    } catch (error) {
      console.error('激活漫游路径失败:', error);
      return false;
    }
  }

  /**
   * 停用漫游
   */
  deactivate(): void {
    try {
      this._rc.deactivate();
    } catch (error) {
      console.error('停用漫游失败:', error);
    }
  }

  /**
   * 清理所有漫游路径
   */
  clear(): void {
    try {
      this._rc.clear();
    } catch (error) {
      console.error('清理漫游路径失败:', error);
    }
  }

  /**
   * 暂停漫游
   */
  pause(): void {
    try {
      this._rc.pause();
    } catch (error) {
      console.error('暂停漫游失败:', error);
    }
  }

  /**
   * 获取漫游路径
   * @param id 路径ID
   */
  getPath(id: string): any {
    return this._rc.getPath(id);
  }

  /**
   * 移除漫游路径
   * @param id 路径ID
   */
  removePath(id: string): boolean {
    try {
      const path = this._rc.getPath(id);
      if (path) {
        this._rc.removePath(path);
        return true;
      }
      return false;
    } catch (error) {
      console.error('移除漫游路径失败:', error);
      return false;
    }
  }

  /**
   * 更改路径位置
   * @param id 路径ID
   * @param positions 位置数组
   */
  changePath(id: string, positions: BC.Position[]): boolean {
    try {
      const path = this._rc.getPath(id);
      if (path) {
        path.positions = positions;
        return true;
      }
      return false;
    } catch (error) {
      console.error('更改路径位置失败:', error);
      return false;
    }
  }

  /**
   * 更改漫游状态参数
   * @param id 路径ID
   * @param param 漫游参数
   */
  changeState(id: string, param: RoamingPath): boolean {
    try {
      const path = this._rc.getPath(id);
      if (path) {
        path.heading = param.heading;
        path.pitch = param.pitch;
        path.range = param.range;
        path.speed = param.speed;
        return true;
      }
      return false;
    } catch (error) {
      console.error('更改漫游状态失败:', error);
      return false;
    }
  }

  /**
   * 更改漫游进度
   * @param id 路径ID
   * @param progress 进度值 (0-1 范围)
   */
  changeProgress(id: string, progress: number): boolean {
    try {
      const path = this._rc.getPath(id);
      if (path) {
        // 确保进度值在有效范围内
        progress = Math.max(0, Math.min(1, progress));
        path.progress = progress;
        return true;
      }
      return false;
    } catch (error) {
      console.error('更改漫游进度失败:', error);
      return false;
    }
  }

  /**
   * 更改漫游速度
   * @param id 路径ID
   * @param speed 速度值
   */
  changeSpeed(id: string, speed: number): boolean {
    try {
      const path = this._rc.getPath(id);
      if (path) {
        path.speed = speed;
        return true;
      }
      return false;
    } catch (error) {
      console.error('更改漫游速度失败:', error);
      return false;
    }
  }

  /**
   * 获取路径覆盖物
   * @param id 路径ID
   */
  getOverlay(id: string): BC.Polyline | null {
    try {
      const path = this._rc.getPath(id);
      if (path && path.positions) {
        return new BC.Polyline(path.positions);
      }
      return null;
    } catch (error) {
      console.error('获取路径覆盖物失败:', error);
      return null;
    }
  }

  /**
   * 检查路径是否存在
   * @param id 路径ID
   */
  hasPath(id: string): boolean {
    return !!this._rc.getPath(id);
  }
}
