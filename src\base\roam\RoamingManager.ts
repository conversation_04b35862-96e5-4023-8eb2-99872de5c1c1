/*
 * @Description: 漫游管理
 * @Autor: silei
 * @Date: 2023-08-09 11:50:54
 * @LastEditors: silei
 * @LastEditTime: 2023-08-16 14:24:24
 */

import { RoamingPath } from "./type/RoamingPath";
export class RoamingManager {
  private _rc: BC.RoamingController;
  constructor(protected viewer: BC.Viewer) {
    this._rc = new BC.RoamingController(viewer);
  }



  /**
   * 添加漫游路径
   * @param path
   */
  addPath(path: RoamingPath) {
    const roamPath: any = new BC.RoamingPath(path.points ?? "", {
      heading: path.heading,
      pitch: path.pitch,
      range: path.range,
      speed: path.speed,
    });
    roamPath.id = path.id;
    roamPath.infos = path;
    this._rc.addPath(roamPath);
  }
  /**
   * 激活
   * @param id
   */
  active(id: string, onProgress: any) {
    this.deactivate();
    const path: any = this._rc.getPath(id);
    path.speed = path.infos.speed;
    path &&
      this._rc.activate(path, onProgress);
  }
  /**
   * 停止
   */
  deactivate() {
    this._rc.deactivate();
  }

  clear() {
    this._rc.clear();
  }

  /**
   * 暂停
   */
  pause(){
    this._rc.pause();
  }
 
  /**
   * 继续漫游
   * @param id 路径id
   * @param onProgress 进度回调函数
   */
  continue(id: string, onProgress?: any){
    const path: any = this._rc.getPath(id);
    if (!path) {
      console.warn(`漫游路径 ${id} 不存在`);
      return;
    }

    path.speed = path.infos.speed;
    this._rc.activate(path, onProgress);
  }

  getPath(id:string){
    const path = this._rc.getPath(id);
    return path;
  }
  /**
   * 移除路径
   * @param id 路径id
   */
  removePath(id: string) {
    const path = this._rc.getPath(id);
    path && this._rc.removePath(path);
  }

  changePath(id: string, positions: BC.Position[]) {
    const path = this._rc.getPath(id);
    if (path) {
      path.positions = positions;
    }
  }
  changeState(id:string, param:RoamingPath){
    const path = this._rc.getPath(id);
    if(path){
      path.heading = param.heading;
      path.pitch = param.pitch;
      path.range = param.range;
      path.speed = param.speed;
    }
  }
  changeProgress(id:string, progress:number){
    const path = this._rc.getPath(id);
    if(path){
      path.progress = progress;
    }
  }

  changeSpeed(id: string,speed:number) {
    const path = this._rc.getPath(id);
    if(path){
      path.speed = speed;
    }
  }
 
  getOverlay(id: string) {
    const path = this._rc.getPath(id);
    if (path) {
      const polyline = new BC.Polyline(path.positions);
      return polyline;
    }
    return null;
  }
}
