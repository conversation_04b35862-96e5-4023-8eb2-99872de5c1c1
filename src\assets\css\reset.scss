article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

progress {
  vertical-align: baseline;
}

[hidden],
template {
  display: none;
}

a {
  background: transparent;
  text-decoration: none;
  //color: $text;
}

a:active {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

mark {
  background: #ff0;
  color: #000;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

img {
  border: 0;
  vertical-align: middle;
}

svg:not(:root) {
  width: 1em;
  height: 1em;
}

svg + svg {
  margin-left: 10px;
}

pre {
  overflow: auto;
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
  vertical-align: middle;
}

button,
input,
select {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
html input[type='button'],
input[type='reset'],
input[type='submit'] {
  -webkit-appearance: button;
  cursor: pointer;
}

[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type='checkbox'],
input[type='radio'] {
  box-sizing: border-box;
  padding: 0;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  height: auto;
}

input[type='search'] {
  -webkit-appearance: textfield;
  box-sizing: border-box;
}

input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
  resize: vertical;
  vertical-align: top;
}

optgroup {
  font-weight: bold;
}

input,
select,
textarea {
  outline: 0;
}

textarea,
input {
  -webkit-user-modify: read-write-plaintext-only;
}

input::-ms-clear,
input::-ms-reveal {
  display: none;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #999;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999;
}

.placeholder {
  color: #999;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
figure,
form,
blockquote {
  margin: 0;
}

ul,
ol,
li,
dl,
dd {
  margin: 0;
  padding: 0;
}

ul,
ol {
  list-style: none outside none;
}

h1,
h2,
h3 {
  line-height: 2;
  font-weight: normal;
}

h1 {
  font-size: 18px;
}

h2 {
  font-size: 16px;
}

h3 {
  font-size: 14px;
}

i {
  font-style: normal;
}

* {
  box-sizing: border-box;
}

body {
  // font-family: Roboto, sans-serif, PingFang SC, -apple-system, BlinkMacSystemFont, Segoe UI,
  //   Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif,
  //   Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-family:
    PingFang SC,
    Microsoft YaHei;

  &::-webkit-scrollbar {
    // 定义了滚动条整体的样式；
    background: #f7fafc;
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    width: 8px;
    background: rgb(222, 222, 222);
    border-radius: 4px;
  }
}

body::-webkit-scrollbar {
  display: none;
}

.custom-scroll::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
  border-radius: 4px;
}
.custom-scroll::-webkit-scrollbar-thumb {
  width: 4px;
  background-color: rgba($color: #31bab2, $alpha: 0.4);
  border-radius: 4px;
}

// input:-webkit-autofill,
// textarea:-webkit-autofill,
// select:-webkit-autofill {
//   -webkit-text-fill-color: #ededed !important;
//   -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
//   background-color: transparent;
//   background-image: none;
//   transition: background-color 50000s ease-in-out 0s; //背景色透明  生效时长  过渡效果  启用时延迟的时间
// }
// input {
//   background-color: transparent;
// }

// input:-webkit-autofill {
//   -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
//   color: #fff;
//   -webkit-text-fill-color: #ffffff;
//   transition: background-color 50000s ease-in-out 0s !important;
// }

.custom-mainview-table {
  .custom-header-row {
    --el-table-tr-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    color: #9ee2e2;

    .el-table__cell {
      border-bottom: none !important;
    }
  }

  .custom-header-cell {
    background: transparent;
  }

  .normal-row {
    // background: rgba(#08646e, 0.2);
    background: #08646e;
    color: #ffffff;

    .el-table__cell {
      border-bottom: none;
      // padding: 10px 5px;
    }
  }

  .strip-row {
    background: transparent;
    color: #ffffff;

    .el-table__cell {
      border-bottom: none;
      // padding: 10px 5px;
    }
  }
}
