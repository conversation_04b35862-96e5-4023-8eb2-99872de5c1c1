
<template>
  <div class="left-drawer" v-if="drawer">
    <component style="color: white" :is="componentMap[currentCptName]"></component>
  </div>
</template>

<script setup lang="ts">
import LeftDeviceLocation from '@/views/deviceLocation/LeftDeviceLocation.vue'
import LeftRoaming from '@/views/roaming/LeftRoaming.vue'
import LeftPipeLine from '@/views/pipeLine/LeftPipeLine.vue'
import { ModuleDrawer } from './index'
const drawer = ref(false)
const currentCptName = ref('LeftOverallView')
const componentMap: any = {
  LeftDeviceLocation,
  LeftRoaming,
  LeftPipeLine
}
onMounted(() => {
  ModuleDrawer.changeLeftDrawer.subscribe((cptName) => {
    if (cptName === 'LeftPipelineAnalysis') return
    currentCptName.value = cptName
  })
  ModuleDrawer.changeDrawerLeftStatus.subscribe((state) => {
    drawer.value = state
  })
})
</script>

<style lang="scss" scoped>
.show-btn {
  display: none;
}
.hidden-btn {
  display: block;
}
.show-card {
  animation: fade-in 0.3s linear forwards;
}
.hidden-card {
  animation: fade-out 0.3s linear forwards;
}
@keyframes fade-out {
  0% {
    left: 0;
  }

  100% {
    left: 5px;
  }
}
@keyframes fade-in {
  0% {
    left: 450px;
  }
  100% {
    left: 450px;
  }
}

.toggle-btn {
  position: absolute;
  // left: -18px;
  top: 49%;
  background: rgba(22, 58, 79, 0.6);
  color: white;
  border-radius: 0;
  min-width: 15px;
  z-index: 1;
}
</style>
