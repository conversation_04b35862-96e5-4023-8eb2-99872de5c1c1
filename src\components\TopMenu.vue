<!--
 * @Description: 顶部菜单栏
 * @Date: 2023-02-09 09:51:55
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2024-06-21 17:27:24
-->
<template>
  <div class="top-menu">
    <div
      class="sybtn"
      :class="[currentMenu.value == item.value ? 'active' : '']"
      @click="menuChange(item)"
      v-for="item in menuList"
      :key="item.value"
    >
      <!-- <div>{{ item.name }}</div> -->
      {{ item.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ModuleDrawer } from './drawer'
import { useDevice } from '@/stores/device'
import { useModuleCard } from '@/stores/ModuleCard'
import { App } from '@/base/App'
import { MoveRangeLimit } from '@/base/utils/CesiumUtils'
const cardModule = useModuleCard()
const menuList: any = ref<Imenu[]>([
  {
    name: 'GIS一张图',
    value: 'OverallView',
    show: {
      left: false,
      right: false
    }
  },
  {
    name: '设备定位与查询',
    value: 'DeviceLocation',
    show: {
      left: true,
      right: false
    }
  },
  {
    name: '漫游巡检',
    value: 'Roaming',
    show: {
      left: true,
      right: false
    }
  },
  {
    name: '管线管理',
    value: 'PipeLine',
    show: {
      left: true,
      right: false
    }
  },
  {
    name: '管廊分区监控',
    value: 'PartitionMonitoring',
    show: {
      left: false,
      right: false
    }
  }
])
interface Imenu {
  name: string
  value: string
  show: Object
}
const currentMenu = ref<Imenu>({
  name: 'GIS一张图',
  value: 'OverallView',
  show: {
    left: false,
    right: false
  }
})
const menuChange = (menuItem: Imenu) => {
  currentMenu.value = menuItem
  const cardName = menuItem.value
  const isShow: any = menuItem.show
  useDevice().show = false
  cardModule.closeCard('key')
  ModuleDrawer.changeLeftDrawer.next(`Left${cardName}`)
  ModuleDrawer.changeRightDrawer.next(`Right${cardName}`)
  ModuleDrawer.changeDrawerLeftStatus.next(isShow.left)
  ModuleDrawer.changeDrawerRightStatus.next(isShow.right)
  if (menuItem.value === 'OverallView') {
    useModuleCard().changeCard({
      name: 'LayerTreeCard'
    })
    useIsAMap().change(true)
    App.getInstance().resetView()
    setLabelLayerVisible(true, '分区')
    setLabelLayerVisible(true, '管廊监控中心')
    // MoveRangeLimit.openLimit()
  } else {
    useIsAMap().change(false)
    setLabelLayerVisible(false, '分区')
    setLabelLayerVisible(false, '管廊监控中心')
    // MoveRangeLimit.closeLimit()
  }
}
const setLabelLayerVisible = (bool: boolean, title: string) => {
  const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === title)
  if (layers.length > 0) {
    layers[0].show = bool
  }
}
onMounted(() => {})
</script>

<style lang="scss" scoped>
.top-menu {
  height: 100%;
  display: flex;
  align-items: center;
}
.sybtn {
  cursor: pointer;
  height: 60px;
  // font: 20px normal Source-CN-Regular;
  font-size: 15px;
  line-height: 60px;
  padding: 0 20px;
  color: $font-color;
  transition: all 0.3s;
  &:hover {
    color: $font-color;
    background-color: $menu-hover-bg;
  }
  div {
    text-align: center;
  }
}
.active {
  color: $menu-active;
  background-color: $font-color;
}
// .active::before {
//   content: '';
//   position: absolute;
//   top: 44px;
//   left: 50%;
//   transform: translateX(-50%);
//   width: 52px;
//   height: 4px;
//   border-radius: 2px;
//   opacity: 1;
//   background: $font-color-active;
//   z-index: 11;
//   border: none;
// }
</style>
