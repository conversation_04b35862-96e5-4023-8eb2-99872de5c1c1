<!--
 * @Description: 
 * @Date: 2024-03-27 10:11:20
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-03-28 17:16:56
-->
<template>
  <div class="device-detial">
    <div class="redio">
      <img :src="getImages('header-radio.png')" alt="" />
    </div>
    <div class="plane">
      <div class="header">
        <div class="icon"></div>
        <div class="device-name">{{ useDevice().deviceData['类型'] }}</div>
      </div>
      <div class="content">
        <div v-for="(item, index) in deviceAttrubute" class="attrubute-item" :key="index">
          <div class="name">{{ item.name }}</div>
          <div class="value" :class="[item.value == '报警' ? 'f_red' : '']">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <div class="path">
      <img :src="getImages('path.png')" alt="" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { useDevice } from '@/stores/device'
import { getImages } from '@/utils/getImages'
const deviceData = useDevice().deviceData
const lonlatToPixel = (lonLat: any) => {
  // const map = App.getInstance().getMap()
  // var pixel = map.getPixelFromCoordinate(lonLat)
  // return pixel
}
const pixelleft = ref('')
const pixeltop = ref('')
const positionChange = () => {
  // const lonlat = [deviceData.lng, deviceData.lat]
  // const pixel = lonlatToPixel(lonlat)
  // pixelleft.value = pixel[0] - 500 + 'px'
  // pixeltop.value = pixel[1] - 120 + 'px'
}
watch(
  () => useDevice().mapMove,
  () => {
    positionChange()
  }
)
const deviceAttrubute = [
  {
    name: '设备编码',
    value: deviceData['编码']
  },
  {
    name: '厂家',
    value: 'YD100234'
  },
  {
    name: '检查仪器',
    value: deviceData['类型']
  },
  {
    name: '设备状态',
    value: deviceData.deviceStatus == 'online' ? '正常' : '报警'
  },
  {
    name: '舱室',
    value: deviceData['舱室'].split('#')[1]
  },
  {
    name: '分区',
    value: deviceData['舱室'].split('#')[0] + '区'
  }
]
onMounted(() => {
  positionChange()
})
</script>
<style lang="scss" scoped>
.device-detial {
  position: absolute;
  z-index: 9;
  left: v-bind(pixelleft);
  top: v-bind(pixeltop);
  display: flex;
}
.plane {
  width: 312px;
  height: 135px;
  background: rgba(69, 118, 123, 0.88);
  border: 1px solid #93ffff;
  .header {
    width: 312px;
    height: 28px;
    border-radius: 1px;
    background: linear-gradient(90deg, rgba(0, 255, 212, 0.47) 0%, rgba(51, 204, 204, 0) 100%);
    display: flex;
    align-items: center;
    .icon {
      margin: 10px;
    }
    .device-name {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 17px;
      color: #ffffff;
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 28px);
    padding: 5px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    align-content: space-around;
  }
}
.attrubute-item {
  display: flex;
  align-items: center;
  .name {
    width: 49px;
    height: 17px;
    font-family: Source Han Sans;
    font-size: 12px;
    font-weight: 350;
    line-height: normal;
    color: #95e4e4;
    margin-right: 8px;
    text-align-last: justify
  }
  .value {
    width: 77px;
    height: 17px;
    font-family: Source Han Sans;
    font-size: 12px;
    font-weight: 350;
    line-height: normal;
    color: #ffffff;
  }
}
.path {
  margin-top: 60px;
  margin-left: -5px;
}
.redio {
  position: absolute;
  left: 270px;
  top: -7px;
}
.f_red {
  color: red !important;
}
</style>
