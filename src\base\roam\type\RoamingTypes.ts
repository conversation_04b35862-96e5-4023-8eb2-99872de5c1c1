/**
 * 漫游相关类型定义
 * @description 统一管理漫游功能的所有类型接口
 */

/**
 * 漫游状态枚举
 */
export enum RoamingStatus {
  IDLE = 'idle',           // 空闲状态
  ACTIVE = 'active',       // 激活漫游中
  PAUSED = 'paused',       // 暂停状态
  STOPPED = 'stopped'      // 停止状态
}

/**
 * 漫游进度回调函数类型
 * @param progress 进度值 (0-1 范围)
 */
export type ProgressCallback = (progress: number) => void;

/**
 * 漫游状态接口
 */
export interface IRoamingState {
  /** 漫游路径代码 */
  code: string;
  /** 当前进度 (0-1 范围) */
  progress: number;
  /** 漫游速度 */
  speed: number;
  /** 漫游状态 */
  status: RoamingStatus;
  /** 进度回调函数 */
  callback?: ProgressCallback;
  /** 暂停时的时间戳 */
  pausedAt?: number;
  /** 漫游开始时间 */
  startedAt?: number;
}

/**
 * 漫游配置接口
 */
export interface IRoamingConfig {
  /** 默认速度 */
  defaultSpeed: number;
  /** 默认视角范围 */
  defaultRange: number;
  /** 默认俯仰角 */
  defaultPitch: number;
  /** 进度恢复延迟时间(ms) */
  progressRestoreDelay: number;
}

/**
 * 设备点位信息接口
 */
export interface IDeviceInfo {
  code: string;
  name: string;
  deviceTypeName: string;
  deviceStatus: string;
  position?: {
    x: number;
    y: number;
    z: number;
  };
}

/**
 * 告警信息接口
 */
export interface IAlarmInfo {
  alarmLevel: string;
  alarmDescription: string;
  alarmTime: string;
}

/**
 * 漫游事件类型
 */
export enum RoamingEventType {
  STARTED = 'started',
  PAUSED = 'paused',
  RESUMED = 'resumed',
  STOPPED = 'stopped',
  PROGRESS_CHANGED = 'progressChanged',
  SPEED_CHANGED = 'speedChanged',
  ERROR = 'error'
}

/**
 * 漫游事件接口
 */
export interface IRoamingEvent {
  type: RoamingEventType;
  data?: any;
  timestamp: number;
}

/**
 * 漫游事件监听器类型
 */
export type RoamingEventListener = (event: IRoamingEvent) => void;
