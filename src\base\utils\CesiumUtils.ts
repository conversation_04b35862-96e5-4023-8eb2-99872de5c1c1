import { App } from "../App"

export const getPrimitiveCollection = (collectionName: string, viewer: any) => {
  let collection: any
  const primitives = filterById(collectionName, viewer)
  if (primitives.length == 0) {
    collection = new Cesium.PrimitiveCollection()
    collection.id = collectionName
    viewer.scene.primitives.add(collection)
  } else {
    collection = primitives[0]
  }
  return collection
}

export const getDataSource = (
  dataSourceName: string,
  _viewer: BC.Viewer
): Cesium.CustomDataSource => {
  let dataSource: Cesium.CustomDataSource
  const ds = <Cesium.CustomDataSource[]>_viewer.dataSources.getByName(dataSourceName)
  if (ds.length == 0) {
    dataSource = new Cesium.CustomDataSource(dataSourceName)
    _viewer.dataSources.add(dataSource)
  } else {
    dataSource = ds[0]
  }
  return dataSource
}
export const getDataSourceAsync = async (
  dataSourceName: string,
  _viewer: BC.Viewer
): Promise<Cesium.CustomDataSource> => {
  let dataSource: Cesium.CustomDataSource
  const ds = <Cesium.CustomDataSource[]>_viewer.dataSources.getByName(dataSourceName)
  if (ds.length == 0) {
    dataSource = new Cesium.CustomDataSource(dataSourceName)
    await _viewer.dataSources.add(dataSource)
  } else {
    dataSource = ds[0]
  }
  return dataSource
}

export const filterById = (id: string, viewer: any) => {
  const arr = []
  const primitives = viewer.scene.primitives
  for (let index = 0; index < primitives.length; index++) {
    const primitive = primitives.get(index)
    if (primitive.id === id) {
      arr.push(primitive)
    }
  }
  return arr
}

export const setHeight = (cartesian: any, height: any) => {
  const { Cesium } = BC.Namespace
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
  cartographic.height = height
  return Cesium.Cartographic.toCartesian(cartographic)
}



export class MoveRangeLimit {
  static removeFunc: any
  static openLimit() {
    const removeFunc: any = this.moveOnRange(
      {
        east: 103.73959290134874,
        north: 29.56281856144387,
        south: 29.501446975004537,
        west: 103.65840248122332
      },
      () => App.getInstance().resetView(),
      5000
    )
    this.removeFunc = removeFunc;
  }

  static closeLimit() {
    this.removeFunc && this.removeFunc()
  }

  /**
 * 在范围内移动地图, 超出时按提供的回调返回主视角, 组件卸载时一定要用返回的函数取消监听
 * @param {*} range 范围
 * @param {*} cb 返回主视角的函数
 * @param {*} maximumZoomDistance 最大高度
 * @param {*} minimumZoomDistance 最小高度
 */
static moveOnRange(range: any, cb: any, maximumZoomDistance: any, minimumZoomDistance?: any) {
  const viewer = App.getInstance().getViewer()
  const originMax = viewer.scene.screenSpaceCameraController.maximumZoomDistance //变焦时相机位置的最大大小。默认为无穷大
  const originMin = viewer.scene.screenSpaceCameraController.minimumZoomDistance
  if (maximumZoomDistance)
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = maximumZoomDistance //为了可以看到变焦时的超出范围的效果
  if (minimumZoomDistance)
    viewer.scene.screenSpaceCameraController.minimumZoomDistance = minimumZoomDistance
  if (typeof cb !== 'function') return
  const throttlingCb = this.throttling(cb, 2000)
  const func = () => {
    //计算镜头范围方法（椭球上的近似可见矩形），该方法会计算当前镜头地理坐标（弧度）范围并返回west,east,north,south 4个范围参数
    const rectangle: any = viewer.camera.computeViewRectangle()
    // 弧度转为经纬度，west为左（西）侧边界的经度，以下类推
    const west = Cesium.Math.toDegrees(rectangle.west)
    const north = Cesium.Math.toDegrees(rectangle.north)
    const east = Cesium.Math.toDegrees(rectangle.east)
    const south = Cesium.Math.toDegrees(rectangle.south)
    // console.log('鼠标', west, north, east, south)
    // console.log(range.west, range.north, range.east, range.south)
    //如果视角超出设置范围则跳转视角
    if (west < range.west || north > range.north || east > range.east || south < range.south) {
      //这里可以添加控制地球的禁止平移等等
      // viewer.scene.screenSpaceCameraController.enableTilt  = false
      throttlingCb()
    } else {
      //这里可以添加控制地球的允许平移等等
      // viewer.scene.screenSpaceCameraController.enableTilt  = true
    }
  }
  //实时监测镜头范围(该方法会一直调用)
  viewer.scene.preRender.addEventListener(func)
  return () => {
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = originMax
    viewer.scene.screenSpaceCameraController.minimumZoomDistance = originMin
    viewer.scene.preRender.removeEventListener(func)
  }
}

static throttling(callback: any, delay: any) {
  let time = 0
  return () => {
    const now: any = new Date()
    if (now - time > delay) {
      callback.call(this)
      time = now
    }
  }
}
}