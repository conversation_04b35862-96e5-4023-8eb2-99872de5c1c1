


import hRequest from '@/utils/http/device_server'
import type { DataType } from '@/utils/http/types'


//获取所有设备列表，data传空对象{}
export const getAllDevicelist = (data?: any) => {
  return hRequest.post<DataType>({
    url: '/admin/device/list',
    data: data
  })
}
//根据设备编码查询设备详情
export const getDeviceByCode = (deviceCode: string) => {
  return hRequest.get<DataType>({
    url: `/admin/device/info/byCode/${deviceCode}`
  })
}
//根据舱室code查询设备树
export const getDeviceTreeByCode = (data: any) => {
  return hRequest.get<DataType>({
    url: `/admin/pipe/monitor/system/type/getRoamDevicesList`,
    params: data
  })
}
//根据舱室code查询设备列表
export const getDeviceListByCode = (data: any) => {
  return hRequest.get<DataType>({
    url: `/admin/pipe/monitor/system/type/getDeviceRoamDetails`,
    params: data
  })
}
//查询分区告警状态
export const getAllPartStatus = () => {
  return hRequest.get<DataType>({
    url: `/admin/pipe/monitor/equipmentMonitoring/selectAllFenQuStatus`
  })
}
//查询设备告警详情
export const getAlarmInfoByCode = (data?: any) => {
  return hRequest.post<DataType>({
    url: `/admin/pipe/monitor/alarm/findZhjkAlarmInfo`,
    data: data
  })
}









// 查询管线列表
export const queryList = () => {
  return hRequest.get<DataType>({
    url: '/admin/pipe/inspection/business/pipelineInfo/list'
  })
}

// 管线新增
export const addPipeline = (data: any) => {
  return hRequest.post<DataType>({
    url: '/admin/pipe/inspection/business/pipelineInfo',
    data
  })
}

// 管线编辑
export const editPipeline = (data: any) => {
  return hRequest.put<DataType>({
    url: '/admin/pipe/inspection/business/pipelineInfo',
    data
  })
}

// 管线删除
export const deletePipeline = (params: string) => {
  return hRequest.delete<DataType>({
    url: `/admin/pipe/inspection/business/pipelineInfo/${params}`
  })
}

// 管线详情
export const queryPipelineDetail = (params: string) => {
  return hRequest.get<DataType>({
    url: `/admin/pipe/inspection/business/pipelineInfo/${params}`
  })
}


// 查询字典类型
export const findDictData = (data: any) => {
  return hRequest.post<DataType>({
    url: '/admin/pipe/base/dictData/findDictData',
    data
  })
}

