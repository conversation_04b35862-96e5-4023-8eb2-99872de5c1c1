<template>
  <div class="title_ flex__ flex__between">
    {{ title }}
  </div>
</template>

<script setup lang="ts">
withDefaults(defineProps<{ title: string }>(), {})
</script>

<style lang="scss" scoped>
.title_ {
  width: 380px;
  height: 33px;
  box-sizing: border-box;
  // padding: 0 10px;
  padding-left: 28px;
  font: 16px YouSheBiaoTi;
  color: #fff;
  line-height: 33px;
  background: url('@/assets/img/title-bg.png') no-repeat;
}
</style>
