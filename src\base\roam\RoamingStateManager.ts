/**
 * 漫游状态管理器
 * @description 统一管理漫游状态，提供状态持久化和事件通知功能
 */

import { 
  type IRoamingState, 
  type IRoamingConfig, 
  RoamingStatus, 
  type ProgressCallback,
  RoamingEventType,
  type IRoamingEvent,
  type RoamingEventListener
} from './type/RoamingTypes';

export class RoamingStateManager {
  private state: IRoamingState | null = null;
  private config: IRoamingConfig;
  private eventListeners: Map<RoamingEventType, RoamingEventListener[]> = new Map();

  constructor(config?: Partial<IRoamingConfig>) {
    this.config = {
      defaultSpeed: 2,
      defaultRange: 3,
      defaultPitch: 0,
      progressRestoreDelay: 200,
      ...config
    };
  }

  /**
   * 初始化漫游状态
   * @param code 漫游路径代码
   * @param callback 进度回调函数
   */
  initializeState(code: string, callback?: ProgressCallback): void {
    this.state = {
      code,
      progress: 0,
      speed: this.config.defaultSpeed,
      status: RoamingStatus.IDLE,
      callback,
      startedAt: Date.now()
    };
    
    this.emitEvent(RoamingEventType.STARTED, { code });
  }

  /**
   * 开始漫游
   */
  start(): void {
    if (!this.state) {
      throw new Error('漫游状态未初始化');
    }
    
    this.state.status = RoamingStatus.ACTIVE;
    this.state.startedAt = Date.now();
    this.emitEvent(RoamingEventType.STARTED, { code: this.state.code });
  }

  /**
   * 暂停漫游
   */
  pause(): void {
    if (!this.state || this.state.status !== RoamingStatus.ACTIVE) {
      console.warn('当前状态无法暂停漫游');
      return;
    }
    
    this.state.status = RoamingStatus.PAUSED;
    this.state.pausedAt = Date.now();
    this.emitEvent(RoamingEventType.PAUSED, { 
      code: this.state.code, 
      progress: this.state.progress 
    });
  }

  /**
   * 继续漫游
   */
  resume(): void {
    if (!this.state || this.state.status !== RoamingStatus.PAUSED) {
      console.warn('当前状态无法继续漫游');
      return;
    }
    
    this.state.status = RoamingStatus.ACTIVE;
    this.state.pausedAt = undefined;
    this.emitEvent(RoamingEventType.RESUMED, { 
      code: this.state.code, 
      progress: this.state.progress 
    });
  }

  /**
   * 停止漫游
   */
  stop(): void {
    if (!this.state) {
      return;
    }
    
    const code = this.state.code;
    this.state.status = RoamingStatus.STOPPED;
    this.emitEvent(RoamingEventType.STOPPED, { code });
    this.state = null;
  }

  /**
   * 更新进度
   * @param progress 进度值 (0-1 范围)
   */
  updateProgress(progress: number): void {
    if (!this.state) {
      return;
    }
    
    // 确保进度值在有效范围内
    progress = Math.max(0, Math.min(1, progress));
    this.state.progress = progress;
    
    // 调用回调函数
    if (this.state.callback) {
      this.state.callback(progress);
    }
    
    this.emitEvent(RoamingEventType.PROGRESS_CHANGED, { 
      code: this.state.code, 
      progress 
    });
  }

  /**
   * 更新速度
   * @param speed 新的速度值
   */
  updateSpeed(speed: number): void {
    if (!this.state) {
      return;
    }
    
    this.state.speed = speed;
    this.emitEvent(RoamingEventType.SPEED_CHANGED, { 
      code: this.state.code, 
      speed 
    });
  }

  /**
   * 获取当前状态
   */
  getState(): IRoamingState | null {
    return this.state ? { ...this.state } : null;
  }

  /**
   * 获取当前进度 (0-1 范围)
   */
  getProgress(): number {
    return this.state?.progress || 0;
  }

  /**
   * 获取当前进度百分比 (0-100 范围)
   */
  getProgressPercent(): number {
    return Math.floor(this.getProgress() * 100);
  }

  /**
   * 设置进度百分比 (0-100 范围)
   * @param percent 百分比值
   */
  setProgressPercent(percent: number): void {
    const progress = percent / 100;
    this.updateProgress(progress);
  }

  /**
   * 检查是否可以暂停
   */
  canPause(): boolean {
    return this.state?.status === RoamingStatus.ACTIVE;
  }

  /**
   * 检查是否可以继续
   */
  canResume(): boolean {
    return this.state?.status === RoamingStatus.PAUSED;
  }

  /**
   * 检查是否处于活动状态
   */
  isActive(): boolean {
    return this.state?.status === RoamingStatus.ACTIVE;
  }

  /**
   * 检查是否处于暂停状态
   */
  isPaused(): boolean {
    return this.state?.status === RoamingStatus.PAUSED;
  }

  /**
   * 获取配置
   */
  getConfig(): IRoamingConfig {
    return { ...this.config };
  }

  /**
   * 添加事件监听器
   * @param eventType 事件类型
   * @param listener 监听器函数
   */
  addEventListener(eventType: RoamingEventType, listener: RoamingEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param listener 监听器函数
   */
  removeEventListener(eventType: RoamingEventType, listener: RoamingEventListener): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 发射事件
   * @param eventType 事件类型
   * @param data 事件数据
   */
  private emitEvent(eventType: RoamingEventType, data?: any): void {
    const event: IRoamingEvent = {
      type: eventType,
      data,
      timestamp: Date.now()
    };
    
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`漫游事件监听器执行错误:`, error);
        }
      });
    }
  }

  /**
   * 清理所有事件监听器
   */
  clearEventListeners(): void {
    this.eventListeners.clear();
  }
}
