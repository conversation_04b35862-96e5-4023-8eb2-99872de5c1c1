const loopChild = (res: any, name: string) => {
  let sphere: any = null
  res.forEach((item: any) => {
    if (item.name === name) {
      sphere = item.sphere
    }
    if (item.children && item.children.length > 0) {
      if (!sphere) {
        sphere = loopChild(item.children, name)
      }
    }
  })
  return sphere
}

export const useCesiumStructure = defineStore('CesiumStructure', () => {
  const cesiumStructure = ref()

  const getCesiumStructure = async () => {
    // const result = await fetch('http://172.22.60.11:88/data/23_ls_gallery/data/01_gallery/scenetree.json')
    const result = await fetch('http://127.0.0.1/data/23_ls_gallery/data/01_gallery/scenetree.json')
    const json = await result.json()
    cesiumStructure.value = json
  }

  const getSphere = (name: string) => {
    return loopChild(cesiumStructure.value.scenes, name)
  }

  return {
    getCesiumStructure,
    getSphere,
    cesiumStructure
  }


})