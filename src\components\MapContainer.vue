<template>
  <div>
    <div id="cesium-container"></div>
    <div v-if="isAMap && !isTool" class="gis-btn-groups">
      <!-- <button @click="draw">绘制</button> -->
      <!-- <button @click="exportJSON">导出</button> -->
      <div>
        <button v-for="item in btns" :key="item.label" @click="openCard(item.enName)">
          {{ item.label }}
        </button>
      </div>
      <!-- <el-form label-position="right" label-width="76px" :model="formData" ref="form">
        <el-form-item label="tx:">
          <el-input-number :step="0.00001" v-model="formData.tx"></el-input-number>
        </el-form-item>
        <el-form-item label="ty:">
          <el-input-number :step="0.0001" v-model="formData.ty"></el-input-number>
        </el-form-item>
        <el-form-item label="tz:">
          <el-input-number v-model="formData.tz"></el-input-number>
        </el-form-item>
        <el-form-item label="rx:">
          <el-input-number :step="0.01" v-model="formData.rx"></el-input-number>
        </el-form-item>
        <el-form-item label="ry:">
          <el-input-number :step="0.01" v-model="formData.ry"></el-input-number>
        </el-form-item>
        <el-form-item label="rz:">
          <el-input-number :step="0.01" v-model="formData.rz"></el-input-number>
        </el-form-item>
      </el-form> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { default as BCGIS } from 'bcgis-sdk/dist/bcgis.base.min'
import BcCore from 'bcgis-sdk/dist/bcgis.core.min'
import PolygonDiffuseGlow from '@/base/effect/PolygonDiffuseGlow'
import LineGlow from '@/base/effect/LineGlow'
import { getDeviceStatusLabel, uuid } from '@/base/utils/CommonUtils'
import { App } from '@/base/App'
import { getPrimitiveCollection } from '@/base/utils/CesiumUtils'
import { useModuleCard } from '@/stores/ModuleCard'
import { getDeviceByCode } from '@/api/device_server'
import RoamTool from '@/base/roam/RoamTool'
import { ElMessage } from 'element-plus'
let viewer: BC.Viewer
let divAttrlayer: BC.HtmlLayer
let clickedPositions: any[] = []
const isGISAMap = useIsAMap()
const { isAMap } = storeToRefs(isGISAMap)
const { isTool } = useTool()
let collection: any
watch(isAMap, (newV, oldV) => {
  console.log(newV)
  !newV && aroundPoint && aroundPoint.stop()
})

const getPipeJsons = async () => {
  await usePipeline().getPipeLineJson()
}
const getFlyJsons = async () => {
  await usePipeline().getFlyJson()
}
const getDeviceJsons = async () => {
  await useDeviceLoc().getDeviceLocJson()
}
const getCesiumStructure = async () => {
  await useCesiumStructure().getCesiumStructure()
}
onMounted(async () => {
  globalThis.BC = BCGIS
  const { Cesium } = BC.Namespace
  globalThis.Cesium = Cesium
  await getPipeJsons()
  await getFlyJsons()
  await getDeviceJsons()
  await getCesiumStructure()
  //@ts-ignore
  BC.use(BcCore)
  //@ts-ignore
  BC.ready(() => {
    //@ts-ignore
    const { Cesium } = BC.Namespace
    viewer = new BC.Viewer('cesium-container')
    var provider = new Cesium.SingleTileImageryProvider({
      url: 'img/world.jpg'
    })
    const _layer = new Cesium.ImageryLayer(provider)
    viewer.delegate.imageryLayers.add(_layer, 0)
    App.getInstance().initViewer(viewer)
    viewer.scene.terrainProvider = new Cesium.EllipsoidTerrainProvider();

    
    const globeRotate = new BC.GlobeRotate(viewer, {
      speed: 1000 * 12,
      duration: 3,
      callback: () => {
        loadLayers()
        App.getInstance().resetView()
        // addPointCollection1()
        // addPointCollection()
        addPartitionWall()
        addPolygonDiffuse()
        addLineGlow()
        // MoveRangeLimit.openLimit()
      },
      context: this
    })
    globeRotate.start()

    // let aroundPoint = new BC.AroundPoint(viewer, '103.71764493049538,29.537896839383126', {
    //   pitch: -41,
    //   range: 6000,
    //   duration: 10,
    //   callback: () => {

    //   }
    // } as any)
    // aroundPoint.start()
    // loadLayers()
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = 5000
    viewer.changeMouseMode(0)
    ;(viewer as any).allowMouseMove = true
    // let img = BC.ImageryLayerFactory.createTdtImageryLayer({
    //   key: 'b254904598a72fd14661de55fa70511d',
    //   style: 'img'
    // })
    // // viewer.addBaseLayer(img)
    // addBingMap()
    if ((Cesium.FeatureDetection as any).supportsImageRenderingPixelated()) {
      //判断是否支持图像渲染像素化处理
      viewer.delegate.resolutionScale = window.devicePixelRatio
    }
    //抗锯齿
    viewer.scene.postProcessStages.fxaa.enabled = true
    const light = new Cesium.DirectionalLight({
      direction: new Cesium.Cartesian3(0.354925, -0.890918, -0.283358)
    })
    light.intensity = 0.5
    viewer.scene.light = light
    // addTerrain()
    collection = new Cesium.PointPrimitiveCollection()
    viewer.scene.primitives.add(collection)
    divAttrlayer = new BC.HtmlLayer('DivAttrPanel')
    viewer.addLayer(divAttrlayer)
    window.roamTool = new RoamTool(viewer)
    window.addEventListener('message', (evt) => {
      const data = JSON.parse(evt.data)
      if (!data.funcName) return

      if (data.funcName === 'startRoaming') {
        window.roamTool.startRoaming(data.cabin)
        window.roamTool.setLabelLayerVisible(false, '分区')
        window.roamTool.setLabelLayerVisible(false, '管廊监控中心')
      }
      if (data.funcName === 'continueRoaming') {
        // 继续漫游，如果需要进度回调可以在这里添加
        window.roamTool.continueRoaming(data.cabin)
      }
      if (data.funcName === 'pauseRoaming') {
        window.roamTool.pauseRoaming()
      }
      if (data.funcName === 'stopRoaming') {
        window.roamTool.stopRoaming()
        window.roamTool.setLabelLayerVisible(true, '分区')
        window.roamTool.setLabelLayerVisible(true, '管廊监控中心')
      }
      if (data.funcName === 'setLabelVisible') {
        window.roamTool.setLabelLayerVisible(data.visible, '分区')
        window.roamTool.setLabelLayerVisible(data.visible, '管廊监控中心')
      }
      if (data.funcName === 'resetView') {
        window.roamTool.stopRoaming()
        window.roamTool.setLabelLayerVisible(true, '分区')
        window.roamTool.setLabelLayerVisible(true, '管廊监控中心')
        App.getInstance().resetView()
      }
    })
    // convertTestData();
    // addTileset()
    // addTileset1()
    // loadPipePrimitive()
    // testPipePrimitive()
    // testCartesianTransform()

    viewer.on(
      // 鼠标左键
      BC.MouseEventType.LEFT_CLICK,
      (value: any) => {
        // console.log(value)
        // clickedPositions.push([value.wgs84Position.lng, value.wgs84Position.lat])
        console.log(
          `${value.wgs84Position.lng}, ${value.wgs84Position.lat}, ${value.wgs84Position.alt}`
        )
        //获取camera的heading pitch roll
        // const carto = Cesium.Cartographic.fromCartesian(viewer.scene.camera.position)
        // console.log('摄像头参数：---------------------')
        // console.log(Cesium.Math.toDegrees(carto.longitude))
        // console.log(Cesium.Math.toDegrees(carto.latitude))
        // console.log(carto.height)
        // console.log('弧度')
        // console.log(viewer.camera.heading)
        // console.log(viewer.camera.pitch)
        // console.log(viewer.camera.roll)
        // console.log('度数')
        // console.log(Cesium.Math.toDegrees(viewer.camera.heading))
        // console.log(Cesium.Math.toDegrees(viewer.camera.pitch))
        // console.log(Cesium.Math.toDegrees(viewer.camera.roll))
        // let rectangle: any = viewer.camera.computeViewRectangle()
        // // 弧度转为经纬度，west为左（西）侧边界的经度，以下类推
        // let west = Cesium.Math.toDegrees(rectangle.west)
        // let north = Cesium.Math.toDegrees(rectangle.north)
        // let east = Cesium.Math.toDegrees(rectangle.east)
        // let south = Cesium.Math.toDegrees(rectangle.south)
        // console.log('矩形范围：---------------------')
        // console.log(west, north, east, south)
        divAttrlayer && divAttrlayer.clear()
        if (value.feature) {
          // console.log(value.feature)
          // const propertyIds = value.feature.getPropertyIds()
          // propertyIds.forEach((id: any) => {
          //   console.log(id)
          //   console.log(value.feature.getProperty(id))
          // })
          const deviceCode = value.feature.getProperty('name')
          if (deviceCode) {
            //正则表达式匹配'1D01'格式开头的设备编号
            const reg = /^1(D|Z)[0-1][0-9]/
            reg.test(deviceCode) && addDivAttrPanel(value.wgs84Position, deviceCode)
          }
        }
        if (value.target && value.target.id) {
          let primitive = value.target.primitive
          const { Cesium } = BC.Namespace
          // //设置选中的颜色
          // primitive.getGeometryInstanceAttributes(value.target.id).color =
          //   Cesium.ColorGeometryInstanceAttribute.toValue(Cesium.Color.BLACK)
          // //隐藏管线
          // primitive.getGeometryInstanceAttributes(value.target.id).show =
          //   Cesium.ShowGeometryInstanceAttribute.toValue(false)
        }
      },
      this
    )
  })
})

const testCartesianTransform = async () => {
  const { Cesium } = BC.Namespace

  //获取pipelines.geojson数据
  const result = await fetch('data/partResult1.json')
  const data = await result.json()
  data.forEach((item: any) => {
    const cartesians = Cesium.Cartesian3.fromDegreesArrayHeights(item.value)
    console.log(cartesians)
    let arr: any = []
    cartesians.forEach((cartesian) => {
      const translation = new Cesium.Cartesian3(formData.rx, formData.ry, formData.rz) // x、y、z 分别表示平移的距离

      // 将坐标系从东北天坐标系转换为固定坐标系
      const transform = Cesium.Transforms.eastNorthUpToFixedFrame(cartesian)
      // 将平移向量与转换矩阵相乘得到结果
      const result = Cesium.Matrix4.multiplyByPoint(transform, translation, new Cesium.Cartesian3())
      //cartesian转为经纬度
      const cartographic = Cesium.Cartographic.fromCartesian(result)
      const longitude = Cesium.Math.toDegrees(cartographic.longitude)
      const latitude = Cesium.Math.toDegrees(cartographic.latitude)
      const height = cartographic.height

      arr.push(longitude)
      arr.push(latitude)
      arr.push(height)
    })
    item.value = arr
  })
  console.log(data)
}

const testPipePrimitive = async () => {
  const { Cesium } = BC.Namespace

  //获取pipelines.geojson数据
  const result = await fetch('data/partResult1.json')
  const data = await result.json()
  const instances: any[] = []
  let shape = computedCircle(0.05)
  data.forEach((item: any) => {
    const cartesians = Cesium.Cartesian3.fromDegreesArrayHeights(item.value)
    //计算cartesians长度
    // let length = 0
    // cartesians.reduce((prev, curr) => {
    //   const distance = Cesium.Cartesian3.distance(prev, curr)
    //   length += distance
    //   return curr
    // })

    // item.length = Number(length.toFixed(3))
    let polylineVolumeGeometry = new Cesium.PolylineVolumeGeometry({
      polylinePositions: cartesians,
      shapePositions: shape,
      vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT
    })
    let instance = new Cesium.GeometryInstance({
      geometry: polylineVolumeGeometry,
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.WHITE.withAlpha(0.5)),
        show: new Cesium.ShowGeometryInstanceAttribute(true)
      },
      id: uuid()
    })
    instances.push(instance)
  })

  // const positions: any[] = []
  // testdata.forEach((item: any) => {
  //   positions.push(parseFloat(item.qdx))
  //   positions.push(parseFloat(item.qdy))
  //   positions.push(500 + Math.random() * 10)
  // })
  // let shape = computedCircle(3)
  // let polylineVolumeGeometry = new Cesium.PolylineVolumeGeometry({
  //   polylinePositions: Cesium.Cartesian3.fromDegreesArrayHeights(positions),
  //   shapePositions: shape,
  //   vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT
  // })
  //第一种
  // viewer.scene.primitives.add(
  //   new Cesium.Primitive({
  //     geometryInstances: new Cesium.GeometryInstance({
  //       geometry: polylineVolumeGeometry,
  //     }),
  //     appearance: new Cesium.EllipsoidSurfaceAppearance({
  //       aboveGround: false,
  //       material: Cesium.Material.fromType('Stripe')
  //     }),
  //   })
  // );
  //第二种
  // viewer.scene.primitives.add(
  //   new Cesium.Primitive({
  //     geometryInstances: new Cesium.GeometryInstance({
  //       geometry: polylineVolumeGeometry,
  //       attributes: {
  //         // color : Cesium.ColorGeometryInstanceAttribute.fromColor(new Cesium.Color(1.0, 1.0, 1.0, 1.0))
  //         color: Cesium.ColorGeometryInstanceAttribute.fromColor(
  //           Cesium.Color.GREEN.withAlpha(0.5)
  //         ),
  //       },
  //     }),
  //     appearance: new Cesium.PerInstanceColorAppearance({
  //       flat: true,
  //       translucent: false,
  //     }),
  //   })
  // );

  testprimitive = viewer.scene.primitives.add(
    new Cesium.Primitive({
      releaseGeometryInstances: false,
      geometryInstances: instances,
      // appearance: new Cesium.PerInstanceColorAppearance({
      //   flat: true,
      //   translucent: false
      // })
      appearance: new Cesium.MaterialAppearance({
        //不考虑光照，跟entity效果一致
        flat: true,
        material: new Cesium.Material({
          fabric: {
            type: 'Image',
            uniforms: {
              image: 'http://127.0.0.1/data/00_static/pipe.png',
              repeat: new BC.Cartesian2(100, 2)
            }
          }
        })
      })
    })
  )
}
const addTerrain = () => {
  let terrain = BC.TerrainFactory.createTerrain(BC.TerrainType.XYZ, {
    url: 'http://127.0.0.1/data/23_ls_gallery/data/terrain'
  })
  // terrainP = terrain;
  viewer.addTerrain(terrain)
  // viewer.delegate.terrainProvider = Cesium.createWorldTerrain({
  //   requestVertexNormals: true,
  //   requestWaterMask: true,
  // });
}
const computedCircle = (radius: number) => {
  const { Cesium } = BC.Namespace
  const positions = []
  for (let i = 0; i < 36; i++) {
    const radian = Cesium.Math.toRadians(i * 10)
    const x = radius * Math.cos(radian)
    const y = radius * Math.sin(radian)
    positions.push(new Cesium.Cartesian2(x, y))
  }
  return positions
}
const clickFunc = (value: any) => {
  clickedPositions.push(
    ...[value.wgs84Position.lng, value.wgs84Position.lat, value.wgs84Position.alt]
  )
  console.log(clickedPositions)
  collection.add({
    position: Cesium.Cartesian3.fromDegrees(
      value.wgs84Position.lng,
      value.wgs84Position.lat,
      value.wgs84Position.alt
    ),
    color: Cesium.Color.RED,
    pixelSize: 10
  })
}

const draw = () => {
  // testCartesianTransform()
  clickedPositions = []
  const { Cesium } = BC.Namespace

  collection.removeAll()
  viewer.off(BC.MouseEventType.LEFT_CLICK, clickFunc, this)
  viewer.on(
    // 鼠标左键
    BC.MouseEventType.LEFT_CLICK,
    clickFunc,
    this
  )
}
// const exportJSON = () => {
//   const { Cesium } = BC.Namespace
//   // let center = Cesium.Cartesian3.fromDegrees(103.705675697553, 29.5356518473659, 0)

// let center = Cesium.Cartesian3.fromDegrees(116, 39)

//   let enr2ffTransforms = Cesium.Transforms.eastNorthUpToFixedFrame(center)
//   let result1 = Cesium.Matrix4.multiplyByPoint(
//     enr2ffTransforms,
//     new Cesium.Cartesian3(0, 0, 0),
//     new Cesium.Cartesian3()
//   )
//   console.log(result1)
//   debugger;
//   let cartographic = Cesium.Cartographic.fromCartesian(result1)
//   console.log({
//     longitude: Cesium.Math.toDegrees(cartographic.longitude),
//     latitude: Cesium.Math.toDegrees(cartographic.latitude)
//   })
// }

const addPartitionWall = async () => {
  const response = await fetch('data/partition2.json')

  const json = await response.json()
  json.forEach((degreesArray: any) => {
    //数组转字符串
    addWall(degreesArray)
  })
}
const addPointCollection = async () => {
  const response = await fetch('data/partition1.json')
  const json = await response.json()
  const { Cesium } = BC.Namespace
  const cartesians = Cesium.Cartesian3.fromDegreesArrayHeights(json)
  const pointCollection = new Cesium.PointPrimitiveCollection()
  cartesians.forEach((cartesian) => {
    pointCollection.add({
      position: cartesian,
      color: Cesium.Color.BLUE,
      pixelSize: 10
    })
  })
  viewer.scene.primitives.add(pointCollection)
}
const addPointCollection1 = async () => {
  const response = await fetch('data/pipe.json')
  const json = await response.json()
  const { Cesium } = BC.Namespace
  const cartesians = Cesium.Cartesian3.fromDegreesArrayHeights(json)
  const pointCollection = new Cesium.PointPrimitiveCollection()
  cartesians.forEach((cartesian) => {
    pointCollection.add({
      position: cartesian,
      color: Cesium.Color.RED,
      pixelSize: 10
    })
  })
  viewer.scene.primitives.add(pointCollection)
}

const loadLayers = async () => {
  const data = await fetch('data/layer.json')
  const layerJson = await data.json()
  App.getInstance().LayerManager.addLayers(layerJson)
}

const btns = [
  {
    label: '图层管理',
    enName: 'LayerTreeCard'
  },
  {
    label: '三维量算',
    enName: 'MeasureCard'
  },
  {
    label: '通视分析',
    enName: 'SlightLineCard'
  },
  {
    label: '可视域分析',
    enName: 'ViewShedCard'
  },
  {
    label: '定点观察',
    enName: 'FixedPoint'
  },
  {
    label: '剖切分析',
    enName: 'ClipAnalysisCard'
  }
]

const currModule = ref('LayerTreeCard')
const openCard = (cardName?: string) => {
  aroundPoint && aroundPoint.stop()
  if (cardName && cardName != 'FixedPoint') {
    currModule.value = cardName
    useModuleCard().changeCard({
      name: cardName
    })
  } else {
    openAroundPoint()
    // getAllCarbins([])
    // getDeviceByIds({
    //   devieIds: 'tx_provider1D12TGH02'
    // })

    // queryAllDeviceByCode({
    //   code: 'tonghenanlu_fenqu1_zhc',
    //   dataType: 'ALL'
    // })
  }
}

let aroundPoint: BC.AroundPoint
// let _startLabel: Cesium.Entity;
// let startPick = false;
const openAroundPoint = () => {
  ElMessage.success('点击地图拾取观察点')
  // startPick = true;
  // _startLabel = new Cesium.Entity({
  //     label: {
  //       text: "点击拾取观察点",
  //       font: "12px",
  //       pixelOffset: { x: 0, y: -15 } as any,
  //       disableDepthTestDistance: Number.POSITIVE_INFINITY,
  //       showBackground: true,
  //     },
  //   });
  //   viewer.entities.add(_startLabel)
  // viewer.on(BC.MouseEventType.MOUSE_MOVE, (evt: any) => {
  //   if(startPick) {
  //     let lng = evt.wgs84Position ? evt.wgs84Position.lng : evt.wgs84SurfacePosition.lng;
  //     let lat = evt.wgs84Position ? evt.wgs84Position.lat : evt.wgs84SurfacePosition.lat;
  //     let alt = evt.wgs84Position ? evt.wgs84Position.alt : evt.wgs84SurfacePosition.alt;
  //     _startLabel.position = BC.Cartesian3.fromDegrees(lng, lat, alt) as any;
  //   } else {
  //     _startLabel && viewer.entities.remove(_startLabel)
  //   }
  // }, this)
  viewer.once(
    BC.MouseEventType.LEFT_CLICK,
    (evt: any) => {
      let lng = evt.wgs84Position ? evt.wgs84Position.lng : evt.wgs84SurfacePosition.lng
      let lat = evt.wgs84Position ? evt.wgs84Position.lat : evt.wgs84SurfacePosition.lat
      aroundPoint = new BC.AroundPoint(viewer, `${lng},${lat}`, {
        pitch: -41,
        range: 2000
      } as any)
      // startPick = false;
      aroundPoint.start()
    },
    this
  )
}

const formData = reactive({
  tx: 103.699218, //模型中心X轴坐标（经度，单位：十进制度）
  ty: 29.526,
  tz: -39,
  rx: 0,
  ry: 0,
  rz: 32
})
let tilesetleshan: any
let testprimitive: any
watch(
  () => formData,
  (value) => {
    update3dtilesMaxtrix2(testprimitive)
  },
  { deep: true }
)
const update3dtilesMaxtrix2 = (primitive: any) => {
  // 定义平移的距离
  const { Cesium } = BC.Namespace
  const translation = new Cesium.Cartesian3(formData.rx, formData.ry, formData.rz) // x、y、z 分别表示平移的距离
  // 获取模型的中心点, 你也可以访问boundingSphere来获取外接球，但这个属性需要在primitive 构建时指定，否则就只有访问这个私有属性
  const center = primitive._boundingSpheres[0].center
  // 将坐标系从东北天坐标系转换为固定坐标系
  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(center)
  // 将平移向量与转换矩阵相乘得到结果
  const result = Cesium.Matrix4.multiplyByPoint(transform, translation, new Cesium.Cartesian3())
  // 将结果与中心点相减，得到相对于世界坐标系的平移向量
  const world_translation = Cesium.Cartesian3.subtract(result, center, new Cesium.Cartesian3())
  // 使用平移向量创建模型矩阵，并将其赋值给p的modelMatrix属性
  primitive.modelMatrix = Cesium.Matrix4.fromTranslation(world_translation)
  console.log(primitive.geometryInstances)
}
//平移、贴地、旋转模型
const update3dtilesMaxtrix = (tileset: any) => {
  //旋转
  var mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(formData.rx))
  var my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(formData.ry))
  var mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(formData.rz))
  var rotationX = Cesium.Matrix4.fromRotationTranslation(mx)
  var rotationY = Cesium.Matrix4.fromRotationTranslation(my)
  var rotationZ = Cesium.Matrix4.fromRotationTranslation(mz)
  //平移
  var position = Cesium.Cartesian3.fromDegrees(formData.tx, formData.ty, formData.tz)
  var m = Cesium.Transforms.eastNorthUpToFixedFrame(position)
  //旋转、平移矩阵相乘
  Cesium.Matrix4.multiply(m, rotationX, m)
  Cesium.Matrix4.multiply(m, rotationY, m)
  Cesium.Matrix4.multiply(m, rotationZ, m)
  //赋值给tileset
  console.log(tileset)
  tileset.modelMatrix = m
}

const addPolygonDiffuse = () => {
  const { Cesium } = BC.Namespace
  let longitude = 103.70594876490921
  let latitude = 29.535687702609643
  // let longitude = 103.700705907466
  // let latitude = 29.529032924684874

  let position = Cesium.Cartesian3.fromDegrees(longitude, latitude, 20)
  PolygonDiffuseGlow.createPolygonDiffuse({
    viewer: viewer,
    center: position,
    radius: 200,
    translucent: true,
    speed: 100,
    extrudedHeight: 300,
    xyScale: 1
  })

  // TowerGlow.createTower(viewer, position, 10, 100, undefined)
}

const addLineGlow = async () => {
  const positions: any[] = []
  const response = await fetch('data/lineFlow.json')
  const flyJSON = await response.json()
  flyJSON.forEach((pos: any) => {
    positions.push(pos[0])
    positions.push(pos[1])
    positions.push(30 + Math.random())
  })
  let polyline = new Cesium.PolylineGeometry({
    positions: Cesium.Cartesian3.fromDegreesArrayHeights(positions),
    width: 20
  })
  const geometryInstance = new Cesium.GeometryInstance({
    geometry: polyline
  })
  // let materialOption = {
  //   type: 'TwinkleLineShader',
  //   //半透明
  //   translucent: true,
  //   rate: 0.05,
  //   t_rate: 120,
  //   glint: true,
  //   odColor: Cesium.Color.fromCssColorString('#33cccc')
  // }
  let materialOption = {
    type: 'RunLineShader',
    translucent: true,
    rate: 0.05,
    t_rate: 120,
    glint: false,
    odColor: Cesium.Color.fromCssColorString('#33cccc')
  }

  const line = LineGlow.createLines(geometryInstance, materialOption, false, undefined)
  const primitives = getPrimitiveCollection('LineGlow', viewer)
  primitives.add(line)
  viewer.scene.primitives.add(primitives)
}

//点击设备查看设备详情
const addDivAttrPanel = async (lnglat: any, deviceCode: string) => {
  const result = await getDeviceByCode(deviceCode)
  if (result.code === '00001') {
    ElMessage.error(result.message)
  } else if (result.code === '00000') {
    divAttrlayer && divAttrlayer.clear()
    const position = new BC.Position(lnglat.lng, lnglat.lat, lnglat.alt)
    let spanAttrs = `
    <div>设备编码：${result.data.code}</div>
    <div>设备名称：${result.data.name}</div>
    <div>设备类型：${result.data.deviceTypeName}</div>
    <div>设备状态：${getDeviceStatusLabel(result.data.deviceStatus)}</div>
      `
    let divIcon = new BC.DivIcon(
      position,
      `<div
      class="dynamicPopup"
    >
      <div class="line"></div>
      <div class="main">
        <div class="border1">
          <div class="border2">
            <div class="infoStyle">
              ${spanAttrs}
            </div>
          </div>
        </div>
      </div>
    </div>`
    )
    divIcon.setStyle({
      offset: {
        x: 0,
        y: -240
      }
    })
    divAttrlayer && divAttrlayer.addOverlay(divIcon)
  }
}

const addWall = (points: any[]) => {
  const { Cesium } = BC.Namespace
  const wallInstance = new Cesium.GeometryInstance({
    geometry: Cesium.WallGeometry.fromConstantHeights({
      positions: Cesium.Cartesian3.fromDegreesArrayHeights(points),
      maximumHeight: 70.0,
      minimumHeight: 3.0,
      vertexFormat: new Cesium.MaterialAppearance().vertexFormat
    })
  })
  const image = `img/effect/lly.png`, //选择自己的动态材质图片
    color = Cesium.Color.fromCssColorString('rgba(0, 255, 255, 0.4)'),
    // color = Cesium.Color.RED.withAlpha(0.8),
    speed = 2,
    source =
      'czm_material czm_getMaterial(czm_materialInput materialInput)\n\
{\n\
    czm_material material = czm_getDefaultMaterial(materialInput);\n\
    vec2 st = materialInput.st;\n\
    vec4 colorImage = texture2D(image, vec2(fract((st.t - speed*czm_frameNumber*0.005)), st.t));\n\
    vec4 fragColor;\n\
    fragColor.rgb = color.rgb / 1.0;\n\
    fragColor = czm_gammaCorrect(fragColor);\n\
    material.alpha = colorImage.a * color.a;\n\
    material.diffuse = (colorImage.rgb+color.rgb)/2.0;\n\
    material.emission = fragColor.rgb;\n\
    return material;\n\
}'

  let material = new Cesium.Material({
    fabric: {
      type: 'PolylinePulseLink',
      uniforms: {
        color: color,
        image: image,
        speed: speed
      },
      source: source
    },
    translucent: function () {
      return true
    }
  })

  viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: [wallInstance],
      appearance: new Cesium.MaterialAppearance({
        // material: getWallMaterial1(Cesium.Color.fromCssColorString('rgba(0, 255, 255, 0.4)'))
        material: material
      })
    })
  )
}
</script>

<style scoped lang="scss">
#cesium-container {
  height: 100vh;
}

.custom__group-btn {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
  width: 1px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );
}
.gis-btn-groups {
  // display: none;
  position: absolute;
  top: 116px;
  left: 40px;
  z-index: 999;
  > div {
    display: flex;
    flex-flow: column;
    row-gap: 26px;
  }
  button {
    position: relative;
    width: 160px;
    height: 36px;
    // color: $font-color-info;
    color: #fff;
    letter-spacing: 1px;
    font-size: 16px;
    background: linear-gradient(180deg, rgba(34, 81, 105, 0.4) 0%, rgba(34, 70, 123, 0.53) 100%);
    box-shadow: inset 0px 0px 5px 3px rgba(107, 179, 255, 0.6);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.6499999761581421),
        rgba(0, 0, 0, 0),
        rgba(255, 255, 255, 0.6499999761581421)
      )
      1 1;
    transition: all 0.3s;
    &:hover {
      color: $secondary;
      background: linear-gradient(180deg, rgba(7, 79, 117, 0.88) 0%, rgba(34, 70, 123, 1) 100%);
      box-shadow: inset 0px 0px 5px 3px rgba(107, 179, 255, 0.6);
    }
    &:before {
      left: -1px;
      @extend .custom__group-btn;
    }
    &:after {
      right: -1px;
      @extend .custom__group-btn;
    }
  }
}
</style>
