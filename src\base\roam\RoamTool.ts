import { getAlarmInfoByCode, getAllDevicelist, getDeviceListByCode } from '@/api/device_server'
import { App } from '../App'
import { getDeviceStatusLabel, uuid } from '../utils/CommonUtils'
import { RoamingPath } from './type/RoamingPath'
import { queryAllDeviceByCode } from '@/api/object_server'
import { setHeight } from '../utils/CesiumUtils'
import { RoamingStateManager } from './RoamingStateManager'
import type { ProgressCallback, IDeviceInfo, IAlarmInfo } from './type/RoamingTypes'

/**
 * 漫游工具类
 * @description 提供完整的漫游功能，包括设备点位显示、漫游控制等
 */
export default class RoamTool {
  /** 路径图层 */
  pathLayer: BC.VectorLayer
  /** 数据源 */
  datasource: any
  /** 当前进度值 (0-1 范围) */
  currProgressValue: number = 0
  /** 状态管理器 */
  private stateManager: RoamingStateManager
  constructor(viewer: BC.Viewer) {
    this.pathLayer = new BC.VectorLayer('roamLayer')
    this.pathLayer.addTo(viewer)
    this.stateManager = new RoamingStateManager()

    viewer.dataSources.add(new Cesium.CustomDataSource('roaming')).then((db) => {
      this.datasource = db
    })
  }

  /**
   * 更改漫游速度
   * @param code 路径代码
   * @param speed 速度值
   */
  changeSpeed(code: string, speed: number): void {
    App.getInstance().RoamingManager.changeSpeed(code, speed)
    // 同时更新状态管理器中的速度
    this.stateManager.updateSpeed(speed)
  }

  /**
   * 更改漫游进度
   * @param code 路径代码
   * @param progress 进度值 (0-1 范围)
   */
  changeProgress(code: string, progress: number): void {
    const success = App.getInstance().RoamingManager.changeProgress(code, progress)
    if (success) {
      this.currProgressValue = progress
      this.stateManager.updateProgress(progress)
    }
  }

  /**
   * 获取当前漫游状态
   */
  getRoamingState() {
    return this.stateManager.getState()
  }

  /**
   * 手动调整进度（用于键盘控制）
   * @param code 路径代码
   * @param delta 进度增量 (百分比，如 +1 或 -1)
   */
  adjustProgress(code: string, delta: number): void {
    const currentPercent = this.stateManager.getProgressPercent();
    const newPercent = Math.max(0, Math.min(100, currentPercent + delta));
    const newProgress = newPercent / 100;

    this.changeProgress(code, newProgress);
  }

  /**
   * 检查是否可以暂停
   */
  canPause(): boolean {
    return this.stateManager.canPause();
  }

  /**
   * 检查是否可以继续
   */
  canResume(): boolean {
    return this.stateManager.canResume();
  }

  /**
   * 检查是否处于活动状态
   */
  isActive(): boolean {
    return this.stateManager.isActive();
  }

  /**
   * 暂停漫游
   * 保存当前状态并停止漫游
   */
  pauseRoaming(): void {
    if (!this.stateManager.canPause()) {
      console.warn('当前状态无法暂停漫游');
      return;
    }

    // 更新当前进度到状态管理器
    this.stateManager.updateProgress(this.currProgressValue);

    // 暂停状态
    this.stateManager.pause();

    console.log('暂停漫游，保存进度：', this.stateManager.getProgressPercent());

    // 停止当前漫游
    App.getInstance().RoamingManager.deactivate();
  }

  /**
   * 继续漫游
   * 重新启动漫游并恢复到保存的进度位置
   * @param code 漫游路径代码
   * @param callback 进度回调函数（可选）
   */
  continueRoaming(code: string, callback?: ProgressCallback): void {
    if (!this.stateManager.canResume()) {
      console.warn('当前状态无法继续漫游');
      return;
    }

    const state = this.stateManager.getState();
    if (!state || state.code !== code) {
      console.warn('没有找到对应的漫游状态，无法继续漫游');
      return;
    }

    const savedProgress = this.stateManager.getProgress();
    console.log('继续漫游，恢复到进度：', this.stateManager.getProgressPercent());

    // 恢复漫游状态
    this.stateManager.resume();

    // 重新启动漫游
    const success = App.getInstance().RoamingManager.activate(code, (value: number) => {
      this.currProgressValue = value;
      this.stateManager.updateProgress(value);
      callback && callback(value);
    });

    if (!success) {
      console.error('重新启动漫游失败');
      return;
    }

    // 延迟恢复到保存的进度位置
    const config = this.stateManager.getConfig();
    setTimeout(() => {
      this.changeProgress(code, savedProgress);
      console.log('恢复进度到：', savedProgress);
    }, config.progressRestoreDelay);
  }

  /**
   * 停止漫游
   * 清理所有状态和资源
   */
  stopRoaming(): void {
    // 停止状态管理器
    this.stateManager.stop();

    // 重置进度值
    this.currProgressValue = 0;

    // 清理资源
    this.datasource && this.datasource.entities.removeAll()
    App.getInstance().RoamingManager.deactivate()
    App.getInstance().RoamingManager.clear()
    this.pathLayer.clear()

    console.log('漫游已停止，所有资源已清理');
  }
  setLabelLayerVisible(bool: boolean, layerName: string) {
    const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === layerName)
    if (layers.length > 0) {
      layers[0].show = bool
    }
  }

  /**
   * 开始漫游
   * @param code 漫游路径代码
   * @param callback 进度回调函数
   */
  startRoaming(code: string, callback?: ProgressCallback): void {
    try {
      // 添加设备点位（如果还没有添加）
      if (this.datasource.entities.values.length === 0) {
        this.addDevicePoint(code)
      }

      // 获取漫游路径数据
      const json = usePipeline().flyJson;
      const features = json.features;
      const feature = features.find((fea: any) => fea.properties.code === code);

      if (!feature) {
        console.error(`未找到代码为 ${code} 的漫游路径`);
        return;
      }

      console.log('根据分区code获取漫游路径：', feature)

      // 构建路径位置
      const points = feature.geometry.coordinates;
      const positions = points.map((item: any) => {
        return new BC.Position(item[0], item[1], feature.properties.height ? feature.properties.height : 1)
      })
      const polyline: any = new BC.Polyline(positions)

      // 清理之前的路径
      this.pathLayer.clear()

      // 创建漫游路径
      const config = this.stateManager.getConfig();
      const path = new RoamingPath(code)
      path.range = config.defaultRange
      path.speed = config.defaultSpeed
      path.pitch = config.defaultPitch

      // 添加路径到管理器
      App.getInstance().RoamingManager.addPath(path)
      App.getInstance().RoamingManager.changePath(code, polyline.positions)

      // 初始化状态
      this.currProgressValue = 0;
      this.stateManager.initializeState(code, callback);
      this.stateManager.start();

      // 激活漫游
      const success = App.getInstance().RoamingManager.activate(code, (value: number) => {
        this.currProgressValue = value;
        this.stateManager.updateProgress(value);
        callback && callback(value);
      });

      if (!success) {
        console.error('启动漫游失败');
        this.stateManager.stop();
      } else {
        console.log('漫游已启动，路径代码：', code);
      }
    } catch (error) {
      console.error('开始漫游时发生错误:', error);
      this.stateManager.stop();
    }
  }
  
  /**
   * 添加设备bill
   */
  async addDevicePoint(code: string) {
    const { Cesium } = BC.Namespace
    const res1 = await queryAllDeviceByCode({
      code: code,
      dataType: 'ALL'
    })
    if (res1.code !== '00000') return
    const devices = res1.data.devices
    console.log('根据分区code获取设备：', devices)
    devices.forEach(async (device: any) => {
      const texts: any = []
      texts.push(`设备编码：${device.code}`)
      texts.push(`设备名称：${device.name}`)
      texts.push(`设备类型：${device.deviceTypeName}`)
      texts.push(`设备状态：${getDeviceStatusLabel(device.deviceStatus)}`)
      // const obj = useDeviceLoc().deviceLocJson.find((item: any) => {
      //   return item.id === device.code
      // })
      if(device.deviceStatus === 'WARN') {
        const result = await getAlarmInfoByCode({
          current: 1,
          query: {
            equipmentCode: device.code,
          },
          size: 10
        })
        if(result.code === '00000' && result.data.records.length > 0) {
          const alarmInfo = result.data.records[0];
          texts.push(`告警等级：${alarmInfo.alarmLevel}`)
          texts.push(`告警描述：${alarmInfo.alarmDescription}`)
          texts.push(`告警时间：${alarmInfo.alarmTime}`)
        }
      }


      const sphere = useCesiumStructure().getSphere(device.code);
      console.log(sphere, device)
      if(!sphere) {
        console.log('当前设备无经纬度', device);
        return
      };
      // if(!obj) {
      //   console.log('当前设备无经纬度', device);
      //   return
      // };
      let center = new Cesium.Cartesian3(sphere[0], sphere[1], sphere[2]);
      // console.log(CesiumUtils)
      // center = setHeight(center, 6.5)
      // console.log()
      //cartesian转经纬度
      const cartographic = Cesium.Cartographic.fromCartesian(center)
      const lat = Cesium.Math.toDegrees(cartographic.latitude)
      const lng = Cesium.Math.toDegrees(cartographic.longitude)
      const height = cartographic.height;
      center = setHeight(center, height + 7)
      console.log(lat, lng, height)

      // const pos = obj.pos;
      // const position = Cesium.Cartesian3.fromDegrees(pos.x, pos.y, pos.z - 314)
      this.createCusbilimg(texts, 'type', (result: any) => {
        this.datasource.entities.add({
          name: device.name,
          // type: '漫游弹框',
          position: center,
          billboard: {
            image: result.img,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // width: 200,
            // height: 120,
            scale: 1,
            verticalOrigin:
              result.type === 'red' ? Cesium.VerticalOrigin.CENTER : Cesium.VerticalOrigin.TOP,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(5, 10),
            // color:new Cesium.Color(0,22/255,57/255)
            pixelOffset: new window.Cesium.Cartesian2(result.offsetx, result.offsety)
          }
        })
      })
    })
  }
  splitTextByLength(text: string, length: number) {
    const result = [];
    for (let i = 0; i < text.length; i += length) {
      result.push(text.substring(i, i + length));
    }
    return result;
  }
  createCusbilimg(texts: any, type: any, callback: any) {
    const _that = this;
    const myImage = new Image()
    myImage.crossOrigin = 'Anonymous'
    console.log(type)
    //旋转角度如果大于零，线在上
    myImage.src = 'img/popup_bg14.png'
    const width = 307,
      height = 175
    let canvas: any = document.createElement('canvas')
    const context: any = canvas.getContext('2d')

    const that = this
    myImage.onload = function () {
      canvas.width = width
      canvas.height = height
      context.rect(0, 0, width, height)
      context.clearRect(0, 0, width, height)
      context.drawImage(
        myImage,
        0,
        0,
        canvas.width,
        canvas.height,
        0,
        0,
        canvas.width,
        canvas.height
      )
      context.font = '14px sans-serif'
      context.textAlign = 'left'
      context.fillStyle = '#fff'
      const left = 120
      const top = 30
      const resultTexts: any = [];
      texts.forEach((text: any, index: any) => {
        const isHH = context.measureText(text).width >= 170
        if(isHH) {
          //将文本按长度进行拆分
          console.log(context.measureText(text).width)
          const textArr = _that.splitTextByLength(text, 14);
          const arr: any = [];
          textArr.forEach((item: any, i: any) => {
            if(i === 0) {
              arr.push(item);
            } else {
              arr.push('\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0' + item);
            }
            
          })
          resultTexts.push(...arr)
        } else {
          resultTexts.push(text)
        }
      })
      resultTexts.forEach((text: any, index: any) => {
        that.getTextCanvas(context, text, left, top + index * 22, 30, 170, 3, 60)
      })
      if (callback) {
        callback({
          img: canvas.toDataURL(),
          offsetx: 153,
          offsety: -40,
          type: 'green'
        })
        canvas = undefined
      }
    }
  }
  getTextCanvas(
    ctx: any,
    content: any,
    drawX: any,
    drawY: any,
    lineHeight: any,
    lineMaxWidth: any,
    lineNum: any,
    offsetX: any
  ) {
    let drawTxt = '' // 当前绘制的内容
    let drawLine = 1 // 第几行开始绘制
    let drawIndex = 0 // 当前绘制内容的索引
    // 判断内容是否可以一行绘制完毕
    if (ctx.measureText(content).width <= lineMaxWidth) {
      ctx.fillText(content, drawX, drawY)
    } else {
      for (let i = 0; i < content.length; i++) {
        drawTxt += content[i]
        if (ctx.measureText(drawTxt).width >= lineMaxWidth) {
          if (drawLine >= lineNum) {
            ctx.fillText(content.substring(drawIndex, i) + '..', drawX, drawY)
            break
          } else {
            ctx.fillText(content.substring(drawIndex, i + 1), drawX, drawY)
            drawIndex = i + 1
            drawLine += 1
            drawY += lineHeight
            drawTxt = ''
          }
        } else {
          // 内容绘制完毕，但是剩下的内容宽度不到lineMaxWidth
          if (i === content.length - 1) {
            ctx.fillText(content.substring(drawIndex), drawX + offsetX, drawY)
          }
        }
      }
    }
  }
}
