import { getAlarmInfoByCode, getAllDevicelist, getDeviceListByCode } from '@/api/device_server'
import { App } from '../App'
import { getDeviceStatusLabel, uuid } from '../utils/CommonUtils'
import { RoamingPath } from './type/RoamingPath'
import { queryAllDeviceByCode } from '@/api/object_server'
import { setHeight } from '../utils/CesiumUtils'

export default class RoamTool {
  pathLayer: BC.VectorLayer
  datasource: any
  currProgressValue: any;
  constructor(viewer: BC.Viewer) {
    this.pathLayer = new BC.VectorLayer('roamLayer')
    this.pathLayer.addTo(viewer)
    viewer.dataSources.add(new Cesium.CustomDataSource('roaming')).then((db) => {
      this.datasource = db
    })
  }

  changeSpeed(code: string,speed: number) {
    App.getInstance().RoamingManager.changeSpeed(code, speed)
  }
  changeProgress(code: string,progress: number) {
    App.getInstance().RoamingManager.changeProgress(code, progress)
  }

  pauseRoaming() {
    App.getInstance().RoamingManager.pause()
  }

  continueRoaming(code: string) {
    App.getInstance().RoamingManager.continue(code)
    this.changeProgress(code, this.currProgressValue)
  }

  stopRoaming() {
    // this.datasource && App.getInstance().getViewer().delegate.dataSources.remove(this.datasource)
    this.datasource && this.datasource.entities.removeAll()
    App.getInstance().RoamingManager.deactivate()
    App.getInstance().RoamingManager.clear()
    this.pathLayer.clear()
  }
  setLabelLayerVisible(bool: boolean, layerName: string) {
    const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === layerName)
    if (layers.length > 0) {
      layers[0].show = bool
    }
  }

  /**
   * 开始漫游
   */
  startRoaming(code: string, callback: any) {
    if (this.datasource.entities.values.length === 0) {
      this.addDevicePoint(code)
    }
    const json = usePipeline().flyJson;
    const features = json.features;
    const feature = features.find((fea: any) => fea.properties.code === code);
    console.log('根据分区code获取漫游路径：', feature)
    const points = feature.geometry.coordinates;
    const positions = points.map((item: any) => {
      return new BC.Position(item[0], item[1], feature.properties.height ? feature.properties.height : 1)
    })
    const polyline: any = new BC.Polyline(positions)
    this.pathLayer.clear()
    // this.pathLayer.addOverlay(polyline)
    // const uid = uuid()
    const path = new RoamingPath(code)
    path.range = 3
    path.speed = 2
    path.pitch = 0
    App.getInstance().RoamingManager.addPath(path)
    App.getInstance().RoamingManager.changePath(code, polyline.positions)
    this.currProgressValue = 0;
    App.getInstance().RoamingManager.active(code, (value: number) => {
      this.currProgressValue = value;
      callback && callback(value);
      // console.log(value + '------------------------------------------')
      // progress.value = value * 100;

      // // 更新状态值
      // const path = App.getInstance().RoamingManager.getPath(
      //   selectedItem.value.id
      // );
      // selectedItem.value.heading = path.heading;
      // selectedItem.value.pitch = path.pitch;
      // selectedItem.value.range = path.range;
    })
  }
  
  /**
   * 添加设备bill
   */
  async addDevicePoint(code: string) {
    const { Cesium } = BC.Namespace
    const res1 = await queryAllDeviceByCode({
      code: code,
      dataType: 'ALL'
    })
    if (res1.code !== '00000') return
    const devices = res1.data.devices
    console.log('根据分区code获取设备：', devices)
    devices.forEach(async (device: any) => {
      const texts: any = []
      texts.push(`设备编码：${device.code}`)
      texts.push(`设备名称：${device.name}`)
      texts.push(`设备类型：${device.deviceTypeName}`)
      texts.push(`设备状态：${getDeviceStatusLabel(device.deviceStatus)}`)
      // const obj = useDeviceLoc().deviceLocJson.find((item: any) => {
      //   return item.id === device.code
      // })
      if(device.deviceStatus === 'WARN') {
        const result = await getAlarmInfoByCode({
          current: 1,
          query: {
            equipmentCode: device.code,
          },
          size: 10
        })
        if(result.code === '00000' && result.data.records.length > 0) {
          const alarmInfo = result.data.records[0];
          texts.push(`告警等级：${alarmInfo.alarmLevel}`)
          texts.push(`告警描述：${alarmInfo.alarmDescription}`)
          texts.push(`告警时间：${alarmInfo.alarmTime}`)
        }
      }


      const sphere = useCesiumStructure().getSphere(device.code);
      console.log(sphere, device)
      if(!sphere) {
        console.log('当前设备无经纬度', device);
        return
      };
      // if(!obj) {
      //   console.log('当前设备无经纬度', device);
      //   return
      // };
      let center = new Cesium.Cartesian3(sphere[0], sphere[1], sphere[2]);
      // console.log(CesiumUtils)
      // center = setHeight(center, 6.5)
      // console.log()
      //cartesian转经纬度
      const cartographic = Cesium.Cartographic.fromCartesian(center)
      const lat = Cesium.Math.toDegrees(cartographic.latitude)
      const lng = Cesium.Math.toDegrees(cartographic.longitude)
      const height = cartographic.height;
      center = setHeight(center, height + 7)
      console.log(lat, lng, height)

      // const pos = obj.pos;
      // const position = Cesium.Cartesian3.fromDegrees(pos.x, pos.y, pos.z - 314)
      this.createCusbilimg(texts, 'type', (result: any) => {
        this.datasource.entities.add({
          name: device.name,
          // type: '漫游弹框',
          position: center,
          billboard: {
            image: result.img,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // width: 200,
            // height: 120,
            scale: 1,
            verticalOrigin:
              result.type === 'red' ? Cesium.VerticalOrigin.CENTER : Cesium.VerticalOrigin.TOP,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(5, 10),
            // color:new Cesium.Color(0,22/255,57/255)
            pixelOffset: new window.Cesium.Cartesian2(result.offsetx, result.offsety)
          }
        })
      })
    })
  }
  splitTextByLength(text: string, length: number) {
    const result = [];
    for (let i = 0; i < text.length; i += length) {
      result.push(text.substring(i, i + length));
    }
    return result;
  }
  createCusbilimg(texts: any, type: any, callback: any) {
    const _that = this;
    const myImage = new Image()
    myImage.crossOrigin = 'Anonymous'
    console.log(type)
    //旋转角度如果大于零，线在上
    myImage.src = 'img/popup_bg14.png'
    const width = 307,
      height = 175
    let canvas: any = document.createElement('canvas')
    const context: any = canvas.getContext('2d')

    const that = this
    myImage.onload = function () {
      canvas.width = width
      canvas.height = height
      context.rect(0, 0, width, height)
      context.clearRect(0, 0, width, height)
      context.drawImage(
        myImage,
        0,
        0,
        canvas.width,
        canvas.height,
        0,
        0,
        canvas.width,
        canvas.height
      )
      context.font = '14px sans-serif'
      context.textAlign = 'left'
      context.fillStyle = '#fff'
      const left = 120
      const top = 30
      const resultTexts: any = [];
      texts.forEach((text: any, index: any) => {
        const isHH = context.measureText(text).width >= 170
        if(isHH) {
          //将文本按长度进行拆分
          console.log(context.measureText(text).width)
          const textArr = _that.splitTextByLength(text, 14);
          const arr: any = [];
          textArr.forEach((item: any, i: any) => {
            if(i === 0) {
              arr.push(item);
            } else {
              arr.push('\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0' + item);
            }
            
          })
          resultTexts.push(...arr)
        } else {
          resultTexts.push(text)
        }
      })
      resultTexts.forEach((text: any, index: any) => {
        that.getTextCanvas(context, text, left, top + index * 22, 30, 170, 3, 60)
      })
      if (callback) {
        callback({
          img: canvas.toDataURL(),
          offsetx: 153,
          offsety: -40,
          type: 'green'
        })
        canvas = undefined
      }
    }
  }
  getTextCanvas(
    ctx: any,
    content: any,
    drawX: any,
    drawY: any,
    lineHeight: any,
    lineMaxWidth: any,
    lineNum: any,
    offsetX: any
  ) {
    let drawTxt = '' // 当前绘制的内容
    let drawLine = 1 // 第几行开始绘制
    let drawIndex = 0 // 当前绘制内容的索引
    // 判断内容是否可以一行绘制完毕
    if (ctx.measureText(content).width <= lineMaxWidth) {
      ctx.fillText(content, drawX, drawY)
    } else {
      for (let i = 0; i < content.length; i++) {
        drawTxt += content[i]
        if (ctx.measureText(drawTxt).width >= lineMaxWidth) {
          if (drawLine >= lineNum) {
            ctx.fillText(content.substring(drawIndex, i) + '..', drawX, drawY)
            break
          } else {
            ctx.fillText(content.substring(drawIndex, i + 1), drawX, drawY)
            drawIndex = i + 1
            drawLine += 1
            drawY += lineHeight
            drawTxt = ''
          }
        } else {
          // 内容绘制完毕，但是剩下的内容宽度不到lineMaxWidth
          if (i === content.length - 1) {
            ctx.fillText(content.substring(drawIndex), drawX + offsetX, drawY)
          }
        }
      }
    }
  }
}
