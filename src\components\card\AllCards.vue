
<template>
  <div v-if="mainComponents.length">
    <template v-for="item in mainComponents" :key="item.uuid">
      <component
        :is="item.component"
        :uuid="item.uuid"
        :properties="item.properties"
      ></component>
    </template>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useModuleCard } from "@/stores/ModuleCard";
const ModuleCard = useModuleCard();
const { mainComponents } = storeToRefs(ModuleCard)
</script>

<style scoped></style>
