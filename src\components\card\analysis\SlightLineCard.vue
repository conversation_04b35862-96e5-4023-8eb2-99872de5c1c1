<!--
 * @Description: 通视分析
 * @Date: 2023-08-11 14:10:24
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-08-31 11:02:35
-->
<template>
  <BaseCard :width="'360px'" :title="'通视分析'" :closed-method="close">
    <el-row class="operate-btns">
      <el-button class="operate-btn custom-confirm-btn" @click="clipping">绘制</el-button>
      <el-button class="operate-btn custom-cancel-btn" @click="resetClip">清除</el-button>
    </el-row>
  </BaseCard>
</template>

<script setup lang="ts">
import { App } from '@/base/App'
import { onMounted, onUnmounted } from 'vue'
import BaseCard from '../BaseCard.vue'
const { Cesium } = BC.Namespace

const close = () => {
  slightLine.destroy()
}
onUnmounted(() => {
  App.getInstance().getPlotUtil().stop()
  resetClip()
})
let slightLine: any
onMounted(async () => {
  slightLine = await new (BC as any).SlightLine()
  App.getInstance().getViewer().use(slightLine)
})
const resetClip = () => {
  App.getInstance().getPlotUtil().stop()
  slightLine.clear()
}

const clipping = () => {
  slightLine.drawLine()
}
</script>

<style lang="scss" scoped></style>
