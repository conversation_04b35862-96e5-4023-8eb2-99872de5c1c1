<template>
  <div class="map-footer custom-style">
    <div class="content">
      <div class="select-box">
        <el-select
          v-model="currentPartiton"
          popper-class="custom-select-popper"
          @change="selectChange"
          placeholder="请选择"
        >
          <el-option
            v-for="item in partitionOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </div>
      <div class="area-box">
        <div class="left-arrow" @click="toRight">
          <!-- <img v-if="step == 0" :src="getImages('left-arrow-d.png')" alt="" /> -->
          <img class="cur" :src="getImages('left-arrow-l.png')" alt="" />
        </div>
        <div class="area-content">
          <div class="area-list-box">
            <div
              class="area-item"
              v-for="(item, index) in cabinOptions"
              :class="setClass(item)"
              @click="flyToArea(item)"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="right-arrow" @click="toLeft">
          <!-- <img v-if="step == useStep" :src="getImages('right-arrow-d.png')" alt="" /> -->
          <img class="cur" :src="getImages('right-arrow-l.png')" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { queryChildObjectByCode } from '@/api/object_server'
// import { useDevice } from '@/stores/device'
import { getImages } from '@/utils/getImages'
const props = defineProps({
  type: {
    type: String,
    default: () => ''
  }
})
const footerType = ref(props.type)
watch(
  () => props.type,
  (val) => {
    footerType.value = val
  }
)

const partitionOptions = ref<any>([])
const cabinOptions = ref<any>([])
const currentCabin = ref()
const currentPartiton = ref()

const parParams = ref('tonghenanlu_fenqu')
const getPartitions = async () => {
  const { code, data } = await queryChildObjectByCode({
    // code: 'tonghenanlu_fenqu'
    code: parParams.value
  })
  if (code === '00000') {
    partitionOptions.value = data
    currentPartiton.value = data[0].code
  }
}
const getCabins = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: currentPartiton.value
  })
  if (code === '00000') {
    if (!data.length) return (cabinOptions.value = [])
    cabinOptions.value = data.filter((item: { name: string; code: string }) => {
      return item.name.includes('综合舱') || item.name.includes('电力舱')
    })
    currentCabin.value = data[0].code
  }
}
const boxleft = ref<string>('')
const selectChange = (value: string) => {
  getCabins()
}
const toLeft = () => {
  // console.log(step.value, '----')
  // if (step.value >= useStep.value) {
  //   return
  // }
  // step.value++
  // boxleft.value = -130 * step.value + 'px'
}
const toRight = () => {
  // if (step.value == 0) {
  //   return
  // }
  // step.value--
  // boxleft.value = -130 * step.value + 'px'
}

const setClass = (item: any) => {
  const value = activeArea.value == item.code
  if (value) {
    return 'active'
  } else {
    return ''
  }
}

const activeArea = ref()
const flyToArea = (areaOption: any) => {
  // const iframeNode = document.getElementById('map-iframe') as HTMLIFrameElement
  // if (iframeNode) {
  //   const iwindow = iframeNode.contentWindow

  //   iwindow?.postMessage(
  //     JSON.stringify({ funcName: 'ZoomToRegion', code: currentPartiton.value }),
  //     '*'
  //   )
  // }

  activeArea.value = areaOption.code
  const iframeNode = document.getElementById('map-iframe') as HTMLIFrameElement
  if (iframeNode) {
    const iwindow = iframeNode.contentWindow

    iwindow?.postMessage(
      JSON.stringify({
        funcName: 'CabinZoomToRegion',
        partiCode: currentPartiton.value,
        cabinCode: areaOption.code
      }),
      '*'
    )
  }
}
const resetData = () => {
  boxleft.value = '0px'
  // step.value = 0
  // currentArea.value = options.value[0].children
  // area.value = options.value[0].value
  activeArea.value = {}
}
onMounted(async () => {
  await getPartitions()
  await getCabins()
})

defineExpose({
  resetData
})
</script>
<style lang="scss" scoped>
.map-footer {
  position: absolute;
  left: 0px;
  bottom: 0;
  width: 100%;
  height: 56px;
  background: rgba(#375a80, 0.6);
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
  .content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.select-box {
  width: 160px;
  height: 32px;
}
.area-box {
  width: 840px;
  height: 38px;
  margin-left: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.area-content {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 30px;
}
.area-list-box {
  position: absolute;
  left: v-bind(boxleft);
  display: flex;
  align-items: center;
  padding: 0 2px;
  transition: all 1s;
  .area-item {
    height: 38px;
    border-radius: 2px;
    margin-right: 42px;
    box-sizing: border-box;
    font-size: 14px;
    text-align: center;
    letter-spacing: 0px;
    line-height: 38px;
    color: $secondary;
    padding: 2px 8px;
    border: 1px solid rgba(211, 141, 141, 0);
    background: linear-gradient(180deg, rgba(34, 146, 206, 0.4) 0%, rgba(8, 160, 255, 0) 100%);
    cursor: pointer;
  }
  .active {
    background: linear-gradient(180deg, rgba(34, 163, 206, 0.4) 0%, rgba(8, 169, 255, 0.55) 100%);
    // box-shadow:
    //   0px 0px 3px 1px #00c8fd,
    //   inset 0px 0px 5px 3px rgba(21, 238, 191, 0.4);
  }
}
.cur {
  cursor: pointer;
}
</style>
