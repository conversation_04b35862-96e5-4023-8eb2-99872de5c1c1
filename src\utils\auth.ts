/*
 * @Author: xiao
 * @Date: 2022-07-20 17:28:38
 * @LastEditors: xiao
 * @LastEditTime: 2024-06-20 11:01:07
 * @Description: 浏览器本地存储
 */
class LocalCache {
  setCache(key: string, value: any) {
    window.localStorage.setItem(key, JSON.stringify(value))
    console.log(localStorage)
  }

  getCache(key: string) {
    // obj => string => obj
    const value = window.localStorage.getItem(key)
    if (value !== null) {
      return JSON.parse(value)
    }
  }

  deleteCache(key: string) {
    window.localStorage.removeItem(key)
  }

  clearCache() {
    window.localStorage.clear()
  }
}

export default new LocalCache()

export const getAccessTokenFromCookie = () => {
  const cookieName = 'accessToken'
  const strCookies = document.cookie
  const array = strCookies.split(';')
  for (let i = 0; i < array.length; i++) {
    const item = array[i].split('=')
    const str1 = item[0]
    const str = str1.replace(/^\s*|\s*$/g, '')
    if (str == cookieName) {
      return item[1]
    }
  }
  return null
}
