import { setHeight } from '@/base/utils/CesiumUtils'
import { BaseLayer } from './BaseLayer'
import { uuid } from '@/base/utils/CommonUtils'
import { queryList } from '@/api/device_server'
import { getImages } from '@/utils/getImages'

/*
 * @Description: 模型图层
 * @Autor: silei
 * @Date: 2023-08-03 09:17:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-09-14 16:25:33
 */
export class PipeLayer extends BaseLayer {
  protected declare _delegate: BC.Tileset
  protected declare _primitives: Cesium.PrimitiveCollection
  constructor(options: any) {
    super(options)
  }
  async addToInternal(viewer: BC.Viewer) {
    const { Cesium } = BC.Namespace
    this._primitives = new Cesium.PrimitiveCollection()
    this._viewer.scene.primitives.add(this._primitives)
    const { code, data } = await queryList()
    if (code === 200) {
      const primitives = await this.addPipeLinesByItems(data)
      return primitives
    }
  }

  async updatePipeLinesByItems(items: any, newMaterial: string, oldMaterial: string) {
    items.forEach((item: any, index: number) => {

      const code = item.code;
      //根据code找到所有的管线geojson
      const startPart = code.slice(2, 4)
      const endPart = code.slice(4, 6)
      const other = code.split(`${startPart}${endPart}`)
      const startPartNumber = Number(startPart)
      const endPartNumber = Number(endPart)
      const codes: string[] = []
      for (let i = startPartNumber; i <= endPartNumber; i++) {
        let partCode = i + ''
        if (partCode.length === 1) {
          partCode = `0${i}`
        }
        codes.push(`${other[0]}${partCode}${other[1]}`)
      }

      item.codes = codes

      //根据code找primitive
      if (this.testHex(newMaterial) && this.testHex(oldMaterial)) {
        //颜色变颜色
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (!primitives.id) {
            codes.map((id: string) => {
              primitives.getGeometryInstanceAttributes(`${id}-color`).color =
                Cesium.ColorGeometryInstanceAttribute.toValue(
                  Cesium.Color.fromCssColorString(newMaterial)
                )
              primitives.getGeometryInstanceAttributes(`${id}-color`).show =
                Cesium.ShowGeometryInstanceAttribute.toValue(true)
            })
          }
        }
      }
      //材质变材质
      if (newMaterial.indexOf('png') !== -1 && oldMaterial.indexOf('png') !== -1) {
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (primitives.id) {
            codes.map((id: string) => {
              if (primitives.id.indexOf(id) !== -1) {
                primitives.appearance.material.uniforms.image = getImages(`pipeline/${newMaterial}`)
              }
            })
          }
        }
      }

      //材质变颜色
      if (this.testHex(newMaterial) && oldMaterial.indexOf('png') !== -1) {
        const instances:any = []
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (primitives.id) {
            codes.map((id: string) => {
              if (primitives.id.indexOf(id) !== -1) {
                const instance = new Cesium.GeometryInstance({
                  geometry: primitives.geometryInstances.geometry,
                  attributes: {
                    color: Cesium.ColorGeometryInstanceAttribute.fromColor(
                      Cesium.Color.fromCssColorString(newMaterial)
                    ),
                    show: new Cesium.ShowGeometryInstanceAttribute(true)
                  },
                  id: id + '-color'
                })
                instances.push(instance)
                
              }
            })
            
          }
        }
        const pri = this._primitives.add(
          new Cesium.Primitive({
            geometryInstances: instances,
            releaseGeometryInstances: false,
            appearance: new Cesium.PerInstanceColorAppearance({
              flat: true,
              translucent: false
            })
          })
        )
        //移除原来的primitive
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (primitives.id) {
            codes.map((id: string) => {
              if (primitives.id.indexOf(id) !== -1) {
                this._primitives.remove(primitives)
              }
            })
          }
        }
      }
      //颜色变材质
      if (this.testHex(oldMaterial) && newMaterial.indexOf('png') !== -1) {
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (!primitives.id && primitives.geometryInstances && primitives.geometryInstances.length > 0) {
            codes.map((id: string) => {
              //隐藏管线
              primitives.getGeometryInstanceAttributes(`${id}-color`).show =
                Cesium.ShowGeometryInstanceAttribute.toValue(false)
              const polylineInstance =
                primitives.geometryInstances.length > 0
                  ? primitives.geometryInstances.find((item: any) => item.id === `${id}-color`)
                  : primitives.geometryInstances
              const instance = new Cesium.GeometryInstance({
                geometry: polylineInstance.geometry,
                id: id + '-material'
              })
              //添加新的材质primitive
              const prim = this._primitives.add(
                new Cesium.Primitive({
                  geometryInstances: instance,
                  releaseGeometryInstances: false,
                  appearance: new Cesium.MaterialAppearance({
                    //不考虑光照，跟entity效果一致
                    flat: true,
                    material: new Cesium.Material({
                      fabric: {
                        type: 'Image',
                        uniforms: {
                          image: getImages(`pipeline/${newMaterial}`),
                          repeat: new BC.Cartesian2(100, 2)
                        }
                      }
                    })
                  })
                })
              )
              prim.id = id + '-material'
              
            })
          }
        }
      }
    })
  }

  testHex(hex: string) {
    const regex = /^#[0-9A-F]{6}$/i
    return regex.test(hex)
  }

  async removePipeLineeByItems(items: any) {
    items.forEach((item: any) => {
      const code = item.code;
      //根据code找到所有的管线geojson
      const startPart = code.slice(2, 4)
      const endPart = code.slice(4, 6)
      const other = code.split(`${startPart}${endPart}`)
      const startPartNumber = Number(startPart)
      const endPartNumber = Number(endPart)
      const codes: string[] = []
      for (let i = startPartNumber; i <= endPartNumber; i++) {
        let partCode = i + ''
        if (partCode.length === 1) {
          partCode = `0${i}`
        }
        codes.push(`${other[0]}${partCode}${other[1]}`)
      }

      if (item.material.indexOf('png') !== -1) {
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (primitives.id) {
            codes.map((code: string) => {
              if (primitives.id.indexOf(code) !== -1) {
                this._primitives.remove(primitives)
              }
            })
          }
        }
      } else {
        for (let i = this._primitives.length - 1; i >= 0; i--) {
          const primitives = this._primitives.get(i)
          if (!primitives.id) {
            codes.map((code: string) => {
              primitives.getGeometryInstanceAttributes(`${code}-color`).show =
                Cesium.ShowGeometryInstanceAttribute.toValue(false)
            })
          }
        }
      }
    })
  }

  async addPipeLinesByItems(items: any, isAdd: boolean = false) {
    items.forEach((item: any, index: number) => {
      const code = item.code;
      //根据code找到所有的管线geojson
      const startPart = code.slice(2, 4)
      const endPart = code.slice(4, 6)
      const other = code.split(`${startPart}${endPart}`)
      const startPartNumber = Number(startPart)
      const endPartNumber = Number(endPart)
      const codes: string[] = []
      for (let i = startPartNumber; i <= endPartNumber; i++) {
        let partCode = i + ''
        if (partCode.length === 1) {
          partCode = `0${i}`
        }
        codes.push(`${other[0]}${partCode}${other[1]}`)
      }




      
      item.codes = codes
      const pipeJsons = usePipeline().pipelineJson
      const copyPipeJsons = JSON.parse(JSON.stringify(pipeJsons))
      const filterJsons = copyPipeJsons.filter((feature: any) => {
        return codes.indexOf(feature.code) !== -1
      })
      item.positions =filterJsons
    })
    //判断hex是否为十六进制颜色
    const regex = /^#[0-9A-F]{6}$/i
    const pipeWithColor = items.filter((item: any) => item.material && regex.test(item.material))
    const pipeWithMaterial = items.filter(
      (item: any) => item.material && item.material.indexOf('.png') > -1
    )
    //添加颜色管线
    const primitiveWithColor = await this.addPipePrimitiveWithColor(pipeWithColor)
    const primitiveWithMaterial = await this.addPipePrimitiveWithMaterial(pipeWithMaterial)
    // this._primitives = [primitiveWithColor, ...primitiveWithMaterial, ...this._primitives]
    // return this._primitives
  }

  async addPipePrimitiveWithMaterial(items: any) {
    const primitives: any = []

    await Promise.all(
      items.map(async (item: any, index: number) => {
        const positionsArr = item.positions;
        const shape = this.computedCircle(0.05)
        item.codes.forEach((code: any) => {
          const degressObj = positionsArr.find((item: any) => item.code === code)
          const positions = Cesium.Cartesian3.fromDegreesArrayHeights(degressObj.value);
          const polylineVolumeGeometry = new Cesium.PolylineVolumeGeometry({
            polylinePositions: positions,
            shapePositions: shape,
            vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT
          })
          //一个code对应一个primitive
          const instance = new Cesium.GeometryInstance({
            geometry: polylineVolumeGeometry,
            id: code + '-material'
          })
          const primitive = this._primitives.add(
            new Cesium.Primitive({
              geometryInstances: instance,
              releaseGeometryInstances: false,
              appearance: new Cesium.MaterialAppearance({
                //不考虑光照，跟entity效果一致
                flat: true,
                material: new Cesium.Material({
                  fabric: {
                    type: 'Image',
                    uniforms: {
                      image: getImages(`pipeline/${item.material}`),
                      repeat: new BC.Cartesian2(100, 2)
                    }
                  }
                })
              })
            })
          )
          primitive.id = code + '-material'
          primitives.push(primitive)
        })
      })
    )
    return primitives
  }

  async addPipePrimitiveWithColor(items: any) {
    const instances: any[] = []
    await Promise.all(
      items.map(async (item: any, index: number) => {
        const positionsArr = item.positions;
        const shape = this.computedCircle(0.05)
        item.codes.forEach((code: any) => {
          const degressObj = positionsArr.find((item: any) => item.code === code)
          const positions = Cesium.Cartesian3.fromDegreesArrayHeights(degressObj.value);
          const polylineVolumeGeometry = new Cesium.PolylineVolumeGeometry({
            polylinePositions: positions,
            shapePositions: shape,
            vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT
          })
          const instance = new Cesium.GeometryInstance({
            geometry: polylineVolumeGeometry,
            attributes: {
              color: Cesium.ColorGeometryInstanceAttribute.fromColor(
                Cesium.Color.fromCssColorString(item.material).withAlpha(0.1)
              ),
              show: new Cesium.ShowGeometryInstanceAttribute(true)
            },
            id: code + '-color'
          })
          instances.push(instance)
        })
      })
    )
    //一个primitive对应多个code
    const primitive = this._primitives.add(
      new Cesium.Primitive({
        geometryInstances: instances,
        releaseGeometryInstances: false,
        appearance: new Cesium.PerInstanceColorAppearance({
          flat: true,
          translucent: false
        })
      })
    )
    return primitive
  }

  computedCircle(radius: number) {
    const { Cesium } = BC.Namespace
    const positions = []
    for (let i = 0; i < 36; i++) {
      const radian = Cesium.Math.toRadians(i * 10)
      const x = radius * Math.cos(radian)
      const y = radius * Math.sin(radian)
      positions.push(new Cesium.Cartesian2(x, y))
    }
    return positions
  }
  computeCircle(radius: number) {
    const positions = [];
    for (let i = 0; i <= 360; i++) {
      const radians = Cesium.Math.toRadians(i);
      positions.push(
        new Cesium.Cartesian2(
          radius * Math.cos(radians),
          radius * Math.sin(radians)
        )
      );
    }
    for (let i = 360; i >=0; i--) {
      const radians = Cesium.Math.toRadians(i);
      positions.push(
        new Cesium.Cartesian2(
          radius / 2 * Math.cos(radians),
          radius / 2 * Math.sin(radians)
        )
      );
    }
    return positions;
  }


  getOverlays() {
    return this._primitives
  }

  flyToModel(model: any) {
    const boundingSphere = BC.Namespace.Cesium.BoundingSphere.transform(
      model.boundingSphere,
      model.modelMatrix,
      new BC.Namespace.Cesium.BoundingSphere()
    )
    this._viewer.camera.flyToBoundingSphere(boundingSphere)
  }
  flyTo(): void {
    if (this._delegate?.delegate) {
      this._delegate.readyPromise.then(() => {
        this.flyToModel(this._delegate.delegate)
      })
    }
  }
  removeInternal() {
    // this._viewer.removeLayer(this._tileset)
  }
  showInternal(bool: boolean) {
    this._primitives.show = bool
  }
  
}
