<template>
  <base-card
    :title="properties.title"
    :width="properties.width"
    :initial-value="properties.initialValue"
    :uuid="uuid"
  >
    <div class="pipeline-content">
      <el-form
        :model="formData"
        class="custom-style pipeline-form"
        ref="formRef"
        :rules="rules"
        label-position="left"
        v-loading="formLoading"
        @submit.prevent
        :disabled="props.properties.disabled"
      >
        <el-row :gutter="24">
          <el-col :span="12" class="grid-cell">
            <base-form-item label="管线名称" prop="name" :required="true">
              <el-input
                v-model="formData.name"
                type="text"
                placeholder="请输入"
                clearable
              ></el-input>
            </base-form-item>
          </el-col>
          <el-col :span="12" class="grid-cell">
            <base-form-item label="管线编码" prop="code" :required="true">
              <el-input
                v-model="formData.code"
                type="text"
                placeholder="请选择分区和位置"
                disabled
              ></el-input>
            </base-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" class="grid-cell">
            <base-form-item label="管线类型" prop="type" :required="true">
              <el-select
                v-model="formData.type"
                placeholder="请选择"
                class="full-width-input"
                clearable
                popper-class="custom-select-popper"
              >
                <el-option
                  v-for="item in pipelineType"
                  :key="item.id"
                  :label="item.dictLabel"
                  :value="item.id"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
          <el-col :span="12" class="grid-cell" style="display: flex; align-items: center">
            <base-form-item label="管线长度" prop="length">
              <el-input
                v-model="formData.length"
                type="number"
                placeholder="请输入"
                clearable
                disabled
              ></el-input>
            </base-form-item>
            <span class="length-unit">m</span>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" class="grid-cell">
            <base-form-item label="联系人" prop="contactPerson">
              <el-input
                v-model="formData.contactPerson"
                placeholder="请输入"
                type="text"
                clearable
              ></el-input>
            </base-form-item>
          </el-col>
          <el-col :span="12" class="grid-cell">
            <base-form-item label="联系电话" prop="telephone">
              <el-input
                v-model="formData.telephone"
                placeholder="请输入"
                type="text"
                clearable
              ></el-input>
            </base-form-item>
          </el-col>
        </el-row>
        <base-form-item label="运行状态" prop="currStatus">
          <el-checkbox-group v-model="formData.currStatus">
            <el-checkbox
              v-for="item in runningState"
              :key="item.value"
              :value="item.value"
              @change="changeState(item.value)"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
        </base-form-item>
        <el-row :gutter="24">
          <el-col :span="12" class="grid-cell">
            <base-form-item label="入廊管线单位" class="form-item-unit" prop="unit">
              <el-input
                v-model="formData.unit"
                placeholder="请输入"
                type="text"
                clearable
              ></el-input>
            </base-form-item>
          </el-col>
          <el-col :span="12" class="grid-cell">
            <base-form-item label="管线材质" prop="material" :required="true">
              <img
                v-if="formData.material.indexOf('png') !== -1"
                class="material-img"
                :src="pipelineMaterials.find((item) => item.value === formData.material)?.src"
                alt=""
              />
              <div
                v-else-if="testHex(formData.material)"
                class="material-img"
                :style="{ background: formData.material }"
                alt=""
              />
              <el-select
                v-model="formData.material"
                placeholder="请选择"
                :class="{ 'material-select': formData.material.length }"
                popper-class="custom-select-popper"
                clearable
                @change="changeMaterial"
              >
                <el-option
                  v-for="item in pipelineMaterials"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <img
                    v-if="item.type === 'image'"
                    class="material-option-img"
                    :src="item.src"
                    alt=""
                  />
                  <div
                    v-else-if="item.type === 'hex'"
                    class="material-option-img"
                    :style="{ background: item.value }"
                  />
                </el-option>
              </el-select>
            </base-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10" class="grid-cell">
            <base-form-item label="入廊分区" prop="zoningLocation" :required="true">
              <el-select
                v-model="formData.zoningLocation"
                placeholder="请选择"
                class="full-width-input"
                popper-class="custom-select-popper"
                clearable
              >
                <el-option
                  v-for="(item, index) in cabins"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                  @click="setCode(item.value, 0)"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
          <el-col :span="7" class="fq-col2">
            <base-form-item
              class="transparent-form-item"
              label=""
              prop="startPartition"
              :required="true"
            >
              <el-select
                v-model="formData.startPartition"
                placeholder="请选择"
                class="full-width-input"
                popper-class="custom-select-popper"
                clearable
              >
                <el-option
                  v-for="(item, index) in cabinAreas"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  @click="setCode(item.value, 1)"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
          <el-col :span="7" class="grid-cell">
            <base-form-item
              label="至"
              class="transparent-form-item"
              prop="endPartition"
              :required="true"
            >
              <el-select
                v-model="formData.endPartition"
                placeholder="请选择"
                class="full-width-input"
                popper-class="custom-select-popper"
                clearable
              >
                <el-option
                  v-for="(item, index) in cabinAreas"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  @click="setCode(item.value, 2)"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10" class="grid-cell">
            <base-form-item label="入廊位置" prop="entranceLocation" :required="true">
              <el-select
                v-model="formData.entranceLocation"
                placeholder="请选择"
                class="full-width-input"
                popper-class="custom-select-popper"
                clearable
              >
                <el-option
                  v-for="(item, index) in location"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                  @click="setCode(item.value, 3)"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
          <el-col :span="7" class="grid-cell">
            <base-form-item label="" class="transparent-form-item" prop="rowNum" :required="true">
              <el-select
                v-model="formData.rowNum"
                placeholder="请选择"
                class="full-width-input"
                popper-class="custom-select-popper"
                clearable
              >
                <el-option
                  v-for="(item, index) in locationRows"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  @click="setCode(item.value, 4)"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
          <el-col :span="7" class="grid-cell">
            <base-form-item class="transparent-form-item" label="" prop="number" :required="true">
              <el-select
                v-model="formData.number"
                placeholder="请选择"
                class="full-width-input"
                clearable
                popper-class="custom-select-popper"
              >
                <el-option
                  v-for="(item, index) in locationCols"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  @click="setCode(item.value, 5)"
                ></el-option>
              </el-select>
            </base-form-item>
          </el-col>
        </el-row>
        <el-row justify="space-between" align="middle">
          <span style="color: var(--base-color-light)">维修改造记录</span>
          <el-button class="custom-confirm-btn" @click="addDtoHandler">
            <el-icon>
              <icon-ep-plus></icon-ep-plus>
            </el-icon>
            新增
          </el-button>
        </el-row>
        <div class="table-box custom-scroll">
          <el-table class="custom-table" :data="formData.dtoList" height="100%">
            <el-table-column type="index" label="序号" width="70" />
            <el-table-column prop="time" label="维修时间" show-overflow-tooltip />
            <el-table-column prop="responsiblePerson" label="负责人" />
            <el-table-column
              show-overflow-tooltip
              prop="content"
              min-width="180"
              label="维修内容"
            />
            <el-table-column label="操作" min-width="85">
              <template #default="{ row, index }">
                <el-button link class="custom-link-btn" @click="checkRepairInfo(row)"
                  >详情</el-button
                >
                <el-button class="custom-link-btn" link @click="deleteRepairInfo(row, index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <el-row class="btn-row" justify="end">
        <el-button
          v-if="!props.properties.disabled"
          class="custom-confirm-btn"
          @click="saveHandler"
          v-loading="subBtnLoading"
        >
          保存
        </el-button>
        <el-button class="custom-cancel-btn" @click="() => cardStore.closeCard(uuid)">
          取消
        </el-button>
      </el-row>

      <el-dialog
        title="新增记录"
        :model-value="visible"
        width="25%"
        class="custom-dialog"
        :destroy-on-close="true"
        :before-close="handleClose"
        :close-on-click-modal="false"
        ref="test"
      >
        <el-form
          ref="dtoFormRef"
          :model="dtoForm"
          :rules="dtoFormRules"
          class="custom-style dto-form"
          :disabled="isDisabled"
        >
          <base-form-item label="负责人" prop="responsiblePerson" :required="true">
            <el-input
              v-model="dtoForm.responsiblePerson"
              type="text"
              placeholder="请输入"
              clearable
            ></el-input>
          </base-form-item>

          <base-form-item label="维修内容" prop="content" :required="true">
            <el-input v-model="dtoForm.content" type="textarea" placeholder="请输入" clearable />
          </base-form-item>
          <base-form-item label="时间" :required="true" prop="time">
            <el-date-picker
              v-model="dtoForm.time"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              popper-class="custom-date-picker-popper"
            />
          </base-form-item>
        </el-form>
        <template #footer>
          <div>
            <el-button class="custom-confirm-btn" @click="eventSubmit" v-if="!isDisabled"
              >确 定</el-button
            >
            <el-button @click="handleClose" class="custom-cancel-btn">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </base-card>
</template>

<script setup lang="ts">
import {
  runningState,
  pipelineMaterials,
  cabinAreas,
  cabins,
  location,
  locationCols,
  locationRows
} from '@/constant/baseData'
import { addPipeline, editPipeline, queryPipelineDetail } from '@/api/device_server'
import type { FormInstance } from 'element-plus'
import { App } from '@/base/App'

const props = defineProps<{
  properties: Pipeline.CardProperties
  uuid: string
}>()

const pipelineStore = usePipeline()
const { pipelineType } = storeToRefs(pipelineStore)
const { getPipelineList } = pipelineStore
const cardStore = useModuleCard()

const initFormData = () => {
  return {
    name: '',
    code: '',
    type: '',
    length: 0,
    contactPerson: '',
    telephone: '',
    status: 0,
    currStatus: [0],
    unit: '',
    material: '',
    zoningLocation: '',
    startPartition: '',
    endPartition: '',
    entranceLocation: '',
    rowNum: '',
    number: '',
    dtoList: []
  }
}
const originMaterial = ref('')
const formData = ref<Pipeline.IForm>(initFormData())

const formLoading = ref(false)
const getPipelineDetail = () => {
  if (!props.properties.id) return
  formLoading.value = true
  queryPipelineDetail(props.properties.id)
    .then((res) => {
      if (res.code == 200) {
        formData.value = res.data
        formData.value.currStatus = [formData.value.status]

        selectedList[0] = formData.value.zoningLocation
        selectedList[1] = formData.value.startPartition
        selectedList[2] = formData.value.endPartition
        selectedList[3] = formData.value.entranceLocation
        selectedList[4] = formData.value.rowNum
        selectedList[5] = formData.value.number
        originMaterial.value = formData.value.material
        // materialImg.value = pipelineMaterials.find(
        //   (item) => item.value == formData.value.material
        // )?.src!
      }
    })
    .catch((err) => console.error(err, 'query pipeline detail error'))
    .finally(() => (formLoading.value = false))
}
getPipelineDetail()
const changeState = (value: number) => {
  formData.value.currStatus!.length && (formData.value.currStatus!.length = 0)
  formData.value.currStatus!.push(value)
  formData.value.status = value
}

const rules = {
  name: [{ required: true, message: '请输入', trigger: 'blur' }],
  type: [{ required: true, message: '请选择', trigger: 'blur' }],
  length: [
    {
      pattern: /^[-]?\d+(\.\d+)?$/,
      trigger: ['blur', 'change'],
      message: '请输入数字'
    }
  ],
  telephone: [
    {
      pattern: /^[1][3-9][0-9]{9}$/,
      trigger: ['blur', 'change'],
      message: '号码错误'
    }
  ],
  code: [{ required: true, message: '请选择入廊分区、入廊位置生成编码', trigger: 'blur' }],
  material: [{ required: true, message: '请选择', trigger: 'change' }],
  zoningLocation: [{ required: true, message: '请选择', trigger: 'change' }],
  startPartition: [{ required: true, message: '请选择', trigger: 'change' }],
  endPartition: [{ required: true, message: '请选择', trigger: 'change' }],
  entranceLocation: [{ required: true, message: '请选择', trigger: 'change' }],
  rowNum: [{ required: true, message: '请选择', trigger: 'change' }],
  number: [{ required: true, message: '请选择', trigger: 'change' }]
}

const formRef = ref<FormInstance>()
const subBtnLoading = ref(false)
//toDO:根据选择的管线段查询响应的空间数据，计算长度
const saveHandler = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        subBtnLoading.value = true
        const data = { ...formData.value }

        //计算管线长度
        data.length = parseFloat(data.length.toFixed(2))

        delete data.currStatus
        const result = 'id' in data ? await editPipeline(data) : await addPipeline(data)
        ElMessage.success(result.message)
        cardStore.closeCard(props.uuid)
        getPipelineList()
        //更新或者新增三维管线
        refreshPipeline(data, 'id' in data && data.material !== originMaterial.value)
      } catch (error) {
        console.error(error, 'pipeline submit error')
      } finally {
        subBtnLoading.value = false
      }
    }
  })
}

const refreshPipeline = (data: any, isEdit: boolean) => {
  //三维场景新增一条管线
  const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === '管线')
  if (layers.length > 0) {
    const layer: any = layers[0]
    if (!isEdit) {
      layer.addPipeLinesByItems([data], true)
    } else {
      layer.updatePipeLinesByItems([data], data.material, originMaterial.value)
    }
  }
}

const initDtoForm = () => {
  return {
    time: '',
    content: '',
    responsiblePerson: ''
  }
}
const dtoFormRules = {
  responsiblePerson: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  content: [{ required: true, message: '请输入', trigger: 'blur' }],
  time: [{ required: true, message: '请输入', trigger: 'change' }]
}

const dtoFormRef = ref<FormInstance>()
const dtoForm = ref<Pipeline.IDtoList>(initDtoForm())
const isDisabled = ref(false)
const visible = ref(false)

const addDtoHandler = () => {
  visible.value = true
}

const handleClose = () => {
  visible.value = false
  dtoForm.value = initDtoForm()
}

const eventSubmit = async () => {
  if (!dtoFormRef.value) return
  await dtoFormRef.value.validate(async (valid) => {
    if (valid) {
      formData.value.dtoList.push(dtoForm.value)
      handleClose()
    }
  })
}

// const materialImg = ref('')
const changeMaterial = (value: any) => {
  // const item = pipelineMaterials.find((item) => item.value === value)
  // if (item) {
  //   materialImg.value = item.src
  // } else {
  //   materialImg.value = ''
  // }
}
const testHex = (hex: string) => {
  const regex = /^#[0-9A-F]{6}$/i
  return regex.test(hex)
}
let selectedList: (string | number)[] = Array.from({ length: 6 })
const setCode = (value: string | number, index: number) => {
  selectedList[index] = value
  formData.value.code = selectedList.join('')

  //计算管线长度
  if (formData.value.code.length === 11) {
    const code = formData.value.code
    const startPart = code.slice(2, 4)
    const endPart = code.slice(4, 6)
    const other = code.split(`${startPart}${endPart}`)
    const startPartNumber = Number(startPart)
    const endPartNumber = Number(endPart)
    const codes: string[] = []
    for (let i = startPartNumber; i <= endPartNumber; i++) {
      let partCode = i + ''
      if (partCode.length === 1) {
        partCode = `0${i}`
      }
      codes.push(`${other[0]}${partCode}${other[1]}`)
    }
    const pipeJsons = usePipeline().pipelineJson
    const copyPipeJsons = JSON.parse(JSON.stringify(pipeJsons))
    const filterJsons = copyPipeJsons.filter((feature: any) => {
      return codes.indexOf(feature.code) !== -1
    })
    formData.value.length = 0
    filterJsons.forEach((feature: any) => {
      formData.value.length += feature.length
    })
  }
}

const checkRepairInfo = (row: Pipeline.IDtoList) => {
  visible.value = true
  isDisabled.value = true
  dtoForm.value = row
}

const deleteRepairInfo = (row: Pipeline.IDtoList, index: number) => {
  useDelMessageBox(() => {
    formData.value.dtoList.splice(index, 1)
  })
}
</script>

<style scoped lang="scss">
.pipeline-content {
  padding: 16px;
}
:deep(.pipeline-form) {
  .el-form-item__label {
    width: 80px;
  }
  .form-item-unit {
    .el-form-item__label {
      width: 112px;
    }
  }
  // 部分form label颜色为透明
  .transparent-form-item {
    .el-form-item__label {
      width: unset;
      padding-right: 12px;
    }
    .required-form-label {
      padding: unset;
    }
    .required-form-label::after {
      display: none;
    }
  }
}
:deep(.dto-form) {
  .el-form-item__label {
    width: 76px;
  }
}
.table-box {
  height: 200px;
  margin-top: 12px;
  overflow-y: scroll;
}
.btn-row {
  margin-top: 16px;
  .el-button {
    color: #fff;
  }
}
.fq-col2 {
  padding-right: unset !important;
}

.material-img {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 140px;
  height: 16px;
}
.material-option-img {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 140px;
  height: 16px;
}
:deep(.material-select) {
  .el-select__placeholder {
    opacity: 0;
  }
}
.length-unit {
  color: var(--base-color-light);
  margin-bottom: 18px;
  margin-left: 10px;
}
</style>
