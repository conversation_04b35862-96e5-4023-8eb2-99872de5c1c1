declare namespace Pipeline {
  export interface IDtoList {
    pipelineId?: string
    time: string
    responsiblePerson: string
    content: string
  }
  export interface IForm {
    id?: string
    name: string
    code: string
    type: string
    length: number
    contactPerson: string
    telephone: string
    status: number
    unit: string
    material: string
    zoningLocation: string
    startPartition: string
    endPartition: string
    entranceLocation: string
    rowNum: string
    number: string
    currStatus?: number[]
    dtoList: IDtoList[]
  }
  export interface ListItem {
    id: string
    name: string
    code: string
    type: string
    material: string
    status: number
    length: string
    telephone: string
    gmtModified: string
  }
  export interface CardProperties {
    disabled?: boolean
    id?: string
    title?: string
    width?: string
    initialValue?: {
      x: number
      y: number
    }
  }
}
