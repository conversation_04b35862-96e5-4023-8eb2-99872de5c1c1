/*
 * @Description: 
 * @Date: 2024-03-27 10:15:40
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-03-27 17:35:21
 */
import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useDevice = defineStore('device', () => {
  const mapMove = ref(0)
  const isReload = ref(0)
  const show = ref(false)
  const deviceData:any = ref({})

  return { show, deviceData,mapMove,isReload }
})
