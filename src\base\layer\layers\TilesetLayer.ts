import { BaseLayer } from "./BaseLayer";

/*
 * @Description: 模型图层
 * @Autor: silei
 * @Date: 2023-08-03 09:17:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-09-14 16:25:33
 */
export class TilesetLayer extends BaseLayer {
  protected declare _delegate: BC.Tileset;
  protected declare _tileset: BC.TilesetLayer;
  addToInternal(viewer: BC.Viewer) {
    const tileset = new BC.Tileset(this._options.url, this._options);
    if(this._title === '地下管廊') {
      tileset.setHeight(7)
    }
    const tilesetLayer = new BC.TilesetLayer(this._id);
    tilesetLayer.attr = this.options
    tilesetLayer.addTo(viewer);

    tileset.addTo(tilesetLayer);
    this._tileset = tilesetLayer;
    return tileset;
  }

  getOverlays() {
    return this._tileset.getOverlays();
  }

  flyToModel(model: any) {
    const boundingSphere = BC.Namespace.Cesium.BoundingSphere.transform(
      model.boundingSphere,
      model.modelMatrix,
      new BC.Namespace.Cesium.BoundingSphere()
    );
    this._viewer.camera.flyToBoundingSphere(boundingSphere);
  }
  flyTo(): void {
    if (this._delegate?.delegate) {
      this._delegate.readyPromise.then(()=>{
        this.flyToModel(this._delegate.delegate);
      })
    }
  }
  removeInternal() {
      this._viewer.removeLayer(this._tileset);
  }
}
