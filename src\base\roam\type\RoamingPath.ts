import { uuid } from "@/base/utils/CommonUtils";


/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-08-09 11:53:55
 * @LastEditors: silei
 * @LastEditTime: 2023-08-14 15:06:39
 */
export class RoamingPath {
  id: string;
  name: string = uuid();
  heading:number = 0;
  pitch: number = -45;
  range: number = 1000;
  speed: number = 20;
  points: BC.Position[] = [];
  constructor(id?: string) {
    this.id = id ?? uuid();
  }
}
