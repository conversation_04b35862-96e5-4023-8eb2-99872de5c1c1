<!--
 * @Description: 可视域分析
 * @Date: 2023-08-11 14:13:59
 * @Author: GISerZ
 * @LastEditor: GISerZ
 * @LastEditTime: 2023-09-01 16:24:25
-->
<template>
  <BaseCard :width="'360px'" :title="'可视域分析'" :closed-method="close">
    <el-row class="operate-btns">
      <el-button class="operate-btn custom-confirm-btn" @click="clipping">绘制</el-button>
      <el-button @click="resetClip" class="operate-btn custom-cancel-btn">清除</el-button>
    </el-row>
  </BaseCard>
</template>

<script setup lang="ts">
import { App } from '@/base/App'
import { onMounted } from 'vue'
import BaseCard from '../BaseCard.vue'

const close = () => {
  App.getInstance().getPlotUtil().stop()
  resetClip()
}
const resetClip = () => {
  viewShed?.clear()
}
let viewShed: any
onMounted(async () => {
  viewShed = await new (BC as any).ViewShedFactory()
  App.getInstance().getViewer().use(viewShed)
})
const clipping = () => {
  resetClip();
  viewShed.createViewShed();
};
onUnmounted(() => {
  close()
})
</script>

<style lang="scss" scoped></style>
