import { ElMessageBox } from 'element-plus'
import 'element-plus/theme-chalk/el-message-box.css'

export const useDelMessageBox = (callback: Function, id: string | null = null, tip?: string) => {
  ElMessageBox.confirm(tip ?? '确定要删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass: 'custom-message-box',
    cancelButtonClass: 'custom-cancel-btn',
    confirmButtonClass: 'custom-confirm-btn'
  })
    .then(() => {
      id ? callback(id) : callback()
    })
    .catch(() => console.log('取消'))
}
