<template>
  <div class="left-body">
    <BaseTitle title="分类查询" />
    <div class="left-section">
      <base-visual-panel class="location-section">
        <DeviceLocationTitle title="查询选择" />
        <div>
          <!-- <el-select
            class="select_road"
            @change="selectPartition"
            v-model="currentPartiton"
            size="small"
            placeholder="请选择分区"
          >
            <el-option
              v-for="item in partitionOption"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select> -->
          <el-row :gutter="16" type="flex" justify="space-between" class="select-section">
            <el-col :span="12">
              <el-select
                @change="selectPartition"
                v-model="currentPartiton"
                class="drawer-select"
                placeholder="请选择分区"
                clearable
                @clear="clearPartition"
              >
                <el-option
                  v-for="item in partitionOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select
                @change="selectCabin"
                clearable
                @clear="clearPartition"
                v-model="currentCabin"
                class="drawer-select"
                placeholder="请选择舱室"
              >
                <el-option
                  v-for="item in cabinOptions"
                  :key="item.code"
                  :label="item.typeDetailName"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>
        <DeviceLocationTitle class="layer-title" title="选择设备" />
        <div class="layer-section custom-scroll">
          <el-tree
            ref="layerTreeRef"
            class="layer-tree"
            node-key="id"
            :props="treeProps"
            show-checkbox
            :data="treeData"
            check-on-click-node
            :expand-on-click-node="false"
            @check="handleCheckChange"
          />
        </div>
      </base-visual-panel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getDeviceTreeByCode } from '@/api/device_server'
import { queryChildObjectByCode } from '@/api/object_server'
import { App } from '@/base/App'
import { setHeight } from '@/base/utils/CesiumUtils'
import { useAttrPanel } from '@/stores/DeviceAttrPanel'
import { ElTree } from 'element-plus'
// const partitionOptions = ref<any[]>([])
const selectPartition = async () => {
  await getCabins()
  await getDeviceTree()
}
const clearPartition = () => {}
const currentPartiton = ref()
const partitionOptions = ref<any>([])
const cabinOptions = ref<any>([])
const currentCabin = ref()
// const cabinOptions = ref<any[]>([])
const selectCabin = async () => {
  await getDeviceTree()
}
const layerTreeRef = ref()
interface Tree {
  id: number
  label: string
  isPenultimate?: boolean
  children?: Tree[]
}
onMounted(async () => {
  await getPartitions()
  await getCabins()
  await getDeviceTree()
})

const getPartitions = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: 'tonghenanlu_fenqu'
  })
  if (code === '00000') {
    partitionOptions.value = data
    currentPartiton.value = data[0].code
  }
}
const getCabins = async () => {
  const { code, data } = await queryChildObjectByCode({
    code: currentPartiton.value
  })
  if (code === '00000') {
    cabinOptions.value = data.filter((item: any) => item.code.indexOf('fenqu') !== -1)
    currentCabin.value = cabinOptions.value[0].code
  }
}

const getDeviceTree = async () => {
  const { code, data } = await getDeviceTreeByCode({
    code: currentCabin.value
  })
  if (code === '00000') {
    treeData.value = data
  }
}

const customNodeClass = (data: Tree) => {
  if (data.isPenultimate) {
    return 'is-penultimate'
  }
}
// 设置any 规避类型报错(未解决)
const treeProps: any = {
  label: (data: any, node: any) => {
    if (data.systemName) return data.systemName
    if (data.name) {
      if (data.name.indexOf('#') !== -1) {
        return data.name.split('#')[1]
      }
      return data.name
    }
    if (data.deviceTypeName) return data.deviceTypeName
  },
  children: 'list',
  disabled: function (data: any, node: any) {
    //带子级的节点不能选中
    if (data.code && data.code.length > 0) {
      return false
    } else {
      return true
    }
  },
  class: customNodeClass
}

const treeData = ref<Tree[]>([])
onUnmounted(() => {
  useAttrPanel().clear()
  resetHighLight()
})
const handleCheckChange = (data: any, checked: any, indeterminate: boolean) => {
  console.log(data, checked, indeterminate)
  layerTreeRef.value.setCheckedKeys([data.id])
  if (data.code && data.code.length > 0) {
    //高亮
    // highLightDevice(data.code)
    //定位
    locDevice(data.code)
    //属性面板
  }
}
const resetHighLight = () => {
  let con = [
    ['true', 'vec4(1,1,1,1)'] //其他项
  ]
  const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === '地下管廊')
  if (layers.length > 0) {
    const layer: any = layers[0]
    console.log(layer.getOverlays())
    const overlays = layer.getOverlays()
    if (overlays.length > 0) {
      overlays[0].delegate.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: con
        }
      })
    }
  }
}

const highLightDevice = (code: string) => {
  let con = [
    ['${name} ==="' + code + '" ', 'color("red", 0.9)'], //符合条件项
    // ['true', 'rgba(255,255,255,0.3)'] //其他项
    ['true', 'vec4(1,1,1,0.2)'] //其他项
  ]
  const layers = App.getInstance().LayerManager.find((layer: any) => layer._title === '地下管廊')
  if (layers.length > 0) {
    const layer: any = layers[0]
    console.log(layer.getOverlays())
    const overlays = layer.getOverlays()
    if (overlays.length > 0) {
      overlays[0].delegate.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: con
        }
      })
    }
  }
}
const locDevice = async (code: string) => {
  useAttrPanel().clear()
  const { Cesium } = BC.Namespace
  // const sphere = data.scenes[0].children.find((item: any) => item.name === '1Z01ELS01');
  const sphere = useCesiumStructure().getSphere(code)
  if (!sphere) {
    ElMessage.error('模型内找不到该设备' + code)
    return
  }
  // const sphere = loopChild(data.scenes, '1D01EPAC01')
  let center = new Cesium.Cartesian3(sphere[0], sphere[1], sphere[2])
  //cartesian转经纬度
  const cartographic = Cesium.Cartographic.fromCartesian(center)
  const height = cartographic.height
  center = setHeight(center, height + 7)
  console.log(sphere[3])
  // const tilesets = App.getInstance().LayerManager.find((layer: any) => layer._title === '地下管廊');
  // if (tilesetleshan._orginMatrixInverse && tilesetleshan._root.transform) {
  //   const mat = Cesium.Matrix4.multiply(
  //     tilesetleshan._root.transform,
  //     tilesetleshan._orginMatrixInverse,
  //     new Cesium.Matrix4()
  //   )
  //   center = Cesium.Matrix4.multiplyByPoint(mat, center, new Cesium.Cartesian3())
  // }
  const spheres = new Cesium.BoundingSphere(center, sphere[3] + 7)
  const carbin = code.substring(1, 4)
  let heading = 3.9664737411173787
  let pitch = -0.006663132532399141
  //TODO
  switch (carbin) {
    case 'Z01':
      heading = 4.322605996333334
      pitch = -0.023173234744359172
      break
    case 'Z02':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z03':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z04':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z05':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z06':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z07':
      heading = 3.8362699008966303
      pitch = -0.03142814473542721
      break
    case 'Z08':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z09':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z10':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z11':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z12':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z13':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z14':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'Z15':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D01':
      heading = 3.9413718682020082
      pitch = -0.10906396113663153
      break
    case 'D02':
      heading = 3.9413718682020082
      pitch = -0.10906396113663153
      break
    case 'D03':
      heading = 3.9413718682020082
      pitch = -0.10906396113663153
      break
    case 'D04':
      heading = 3.9413718682020082
      pitch = -0.10906396113663153
      break
    case 'D05':
      heading = 3.9413718682020082
      pitch = -0.10906396113663153
      break
    case 'D06':
      heading = 3.9413718682020082
      pitch = -0.10906396113663153
      break
    case 'D07':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D08':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D09':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D10':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D11':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D12':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D13':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D14':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
    case 'D15':
      heading = 4.18361973807634
      pitch = -0.006663132532399141
      break
  }
  App.getInstance()
    .getViewer()
    .camera.flyToBoundingSphere(spheres, {
      offset: new Cesium.HeadingPitchRange(heading, pitch, 3),
      complete: () => {
        useAttrPanel().addPanel(code)
      }
    })
}
</script>

<style scoped lang="scss">
.left-body {
  width: 380px;
  height: 100%;
}
.left-section {
  height: calc(100% - 33px);
  padding-top: 8px;
}
.location-section {
  width: inherit;
  height: 100%;
  padding: 20px;
  border-radius: 6px;
}

.select-section {
  margin-top: 8px;
}
.layer-title {
  margin-top: 24px;
}
.layer-section {
  max-height: calc(100% - 110px);
  margin-top: 8px;
  overflow-y: scroll;
}
</style>
